using Siepe.Infrastructure.Logging.QueryLogging.Extensions;
using Siepe.NetCore.Infrastructure.Extensions;
using Siepe.Infrastructure.Logging.Extensions;
using Siepe.Infrastructure.HealthChecks.Extensions;
using Siepe.Infrastructure.PubSub.RabbitMq;
using Serilog;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using ILogger = Serilog.ILogger;

namespace Siepe.Service.BloombergRawMessage.BackgroundWorker
{
    public class Program
    {
        public static int Main(string[] args)
        {
            try
            {
                var builder = Host.CreateApplicationBuilder(args);
                var services = builder.Services;
                var config = builder.Configuration;

                // Get service info from configuration
                var serviceName = "BloombergRawMessage.BackgroundService";
                var serviceVersion = "v1";

                // Configure Serilog 
                var logger = new LoggerConfiguration()
                    .ConfigureLogger(serviceName, config)
                    .CreateLogger();

                Log.Logger = logger;

                // Use Serilog for application logging
                builder.Logging.AddSerilog(logger);

                logger.Information("Configuring {ServiceName} ({Version})...", serviceName, serviceVersion);

                // Register services
                services.AddHostedService<Worker>();
                services.LoadDefaultConfigurations(config);
                services.AddQueryLogger(serviceName);
                services.AddServiceLogging(config, serviceName);
                services.AddDefaultHealthChecks(config);
                services.AddRabbitMq(builder.Configuration);

                // Register Bloomberg Raw Message specific services
                services.AddBloombergRawMessageServices(config);

                // Register startup and shutdown events
                services.AddSingleton<IHostedService, ServiceLifetimeLogger>(sp =>
                    new ServiceLifetimeLogger(logger, serviceName, serviceVersion));

                logger.Information("Starting {ServiceName} ({Version})...", serviceName, serviceVersion);

                // Build and run the host
                var host = builder.Build();
                host.Run();
                return 0;
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "Service terminated unexpectedly");
                return 1;
            }
            finally
            {
                Log.CloseAndFlush();
            }
        }
    }

    /// <summary>
    /// Helper class to log service lifecycle events
    /// </summary>
    public class ServiceLifetimeLogger : IHostedService
    {
        private readonly ILogger _logger;
        private readonly string _serviceName;
        private readonly string _serviceVersion;

        public ServiceLifetimeLogger(ILogger logger, string serviceName, string serviceVersion)
        {
            _logger = logger;
            _serviceName = serviceName;
            _serviceVersion = serviceVersion;
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.Information("{ServiceName} ({Version}) started successfully", _serviceName, _serviceVersion);
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.Information("{ServiceName} ({Version}) is stopping...", _serviceName, _serviceVersion);
            return Task.CompletedTask;
        }
    }
}