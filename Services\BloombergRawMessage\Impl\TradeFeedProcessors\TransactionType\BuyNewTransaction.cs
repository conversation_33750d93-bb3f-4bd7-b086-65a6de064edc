﻿using Siepe.GenevaUtility.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class BuyNewTransaction : BaseTransaction<BuyNewType>, ITransactionType
    {
        public void ProcessTrade(Trade trade, TradeFeedConfiguration config)
        {
            GenevaLoader loader = new GenevaLoader();
            loader.TransactionRecords = new GenevaLoaderTransactionRecords();
            BuyNewType transaction = BuildTransactionRecord(trade, config);
            loader.TransactionRecords.Buy_New = new[] { transaction };
            SendCreateMessage(loader);
        }

        public void CancelTrade(string transactionId, Trade trade, TradeFeedConfiguration config)
        {
            BuyDeleteType deleteType = new BuyDeleteType()
            {
                KeyValue = new TransactionRecordsKeyValueType() { Value = transactionId }
            };
            GenevaLoader loader = new GenevaLoader()
            {
                TransactionRecords = new GenevaLoaderTransactionRecords()
                {
                    Buy_Delete = new[] { deleteType }
                }
            };
            _dataProvider.Cancel(transactionId);
        }
    }
}
