using Siepe.Core.DataAccess.RefData;
using Siepe.Core.Entities;
using InstSvc=Instruments.Entities;
using Siepe.Core.DataAccess;
using System.Linq;
using System.Text.Json;
using Siepe.Core.Entities.Logging;
using System.Runtime.CompilerServices;
//using Siepe.Core.DataAccess;


namespace Siepe.aim.instrument
{

    public class InstrumentManager
    {

        public IRefDataProvider _refDataProvider { get; set; }

        public InstrumentManager(IRefDataProvider refDataProvider)
        {
            this._refDataProvider = refDataProvider;
        }

        public InstSvc.IInstrument? SaveInstrument(InstSvc.Instrument inst)
        {
            int refInstId;
            if (inst.Issuer?.Name != null)
            {
                if (inst.Issuer.DataSource == null) 
                    inst.Issuer.DataSource = new InstSvc.DataSource();
                if (String.IsNullOrWhiteSpace(inst.Issuer.DataSource.DataSourceKey))
                    inst.Issuer.DataSource.DataSourceKey = inst.Issuer.Name;
            }
            if (inst is InstSvc.Debt)
            {
                var d = inst as InstSvc.Debt;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.ABS)
            {
                var d = inst as InstSvc.ABS;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.CashFlow)
            {
                var d = inst as InstSvc.CashFlow;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.CDS)
            {
                var d = inst as InstSvc.CDS;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.SwapFinanceLeg)
            {
                var d = inst as InstSvc.SwapFinanceLeg;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.Bond)
            {
                var b = inst as InstSvc.Bond;
                b.LastResetDate = getDatePart(b.LastResetDate);
            }
            if (inst != null)
            {
                logMessage("Inst Type - " + inst.GetType().ToString() + " " + inst.InstrumentType.ToString());
            }
            //*******SAVE INSTRUMENT IN REFERENCE *************************************************************************************************
            if (inst.Issuer?.Id <= 0)
                inst.Issuer.Id = null;
            refInstId = SaveInstrumentInRef(inst);
            logMessage("Ref Inst Id - " + refInstId.ToString());

            //*******PROMOTE INSTRUMENT FROM REFERENCE TO CREATE IN CORE***************************************************************************
            
            string coreInstrumentType = inst.CoreInstrumentType?.Name ?? inst.InstrumentType?.Name;
            var id = inst.CoreInstrumentType?.Id;
            if (inst.CoreInstrumentType?.Id != null)
            {
                var idType = _refDataProvider.GetInstTypes().FirstOrDefault(o => o.InstTypeID == id);
                if (idType != null)
                    coreInstrumentType = idType.Name;
            }
            var coreInstId = CreateInstrumentFromReference(refInstId, coreInstrumentType);
            
            //********MAP INSTRUMENT IDs BETWEEN CORE AND REFERENCE********************************************************************************

            var rInst = _refDataProvider.GetInstMapToCore(refInstId).FirstOrDefault();
            if (rInst != null)
                coreInstId = rInst.InstID;
            logMessage($"Core Inst Id: {coreInstId} : RefInstId: {refInstId} {coreInstrumentType}");
            try
            {
                var instrument = _refDataProvider.GetIInstrument(coreInstId);
                return instrument;
            }
            catch (Exception ex)
            {
                logMessage($"Error retrieving instrument with CoreInstId: {coreInstId}. Exception: {ex.Message}");
                throw;
            }
        }
        private DateTime? getDatePart(DateTime? dt)
        {
            if (dt.HasValue == false) return dt;
            if (dt.Value == DateTime.MinValue) return null;
            if (dt.Value == DateTime.MaxValue) return null;
            if (dt.Value.Year < 100) return null;
            return dt.Value.Date;
        }
        private int SaveInstrumentInRef(InstSvc.Instrument inst)
        {
            var dataSource = inst?.DataSource?.Name;
            if (String.IsNullOrWhiteSpace(dataSource))
                dataSource = "Client Underride";
            return _refDataProvider.CreateInstrumentInRef(inst, dataSource);
        }

        private int CreateInstrumentFromReference(int instIdRef, string coreInstrumentType)
        {
            return _refDataProvider.CreateInstrumentFromRef(instIdRef, coreInstrumentType);
        }

        private void logMessage(string message, [CallerMemberName] string location = "")
        {
            Logger.WriteDiagnostics(new LogDetail()
            {
                Message = message,
                Product = "BloombergRawMessage.InstrumentManager",
                Location = location,
            });
        }


    }
}