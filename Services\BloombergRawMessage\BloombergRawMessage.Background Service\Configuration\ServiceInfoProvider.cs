using Microsoft.Extensions.Options;

namespace Siepe.Service.BloombergRawMessage.BackgroundWorker.Configuration
{
    /// <summary>
    /// Implementation of service information provider using .NET Core configuration
    /// </summary>
    public class ServiceInfoProvider : IServiceInfoProvider
    {
        private readonly BloombergRawMessageSettings _settings;
        private const string ServiceName = "BloombergRawMessage.BackgroundService";

        public ServiceInfoProvider(IOptions<BloombergRawMessageSettings> settings)
        {
            _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        }

        public string GetDomainName()
        {
            return _settings.Domain;
        }

        public string GetVersion()
        {
            return _settings.Version;
        }

        public string GetServiceName()
        {
            return ServiceName;
        }

        public string GetCompany()
        {
            return _settings.Company;
        }

        public string GetLocation()
        {
            return _settings.Location;
        }

        public string GetDefaultIssuer()
        {
            return _settings.DefaultIssuer;
        }

        public bool GetProcessSecMaster()
        {
            return _settings.ProcessSecMaster;
        }
    }
}
