﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\Siepe.Core.DataAccess\Siepe.core.dataaccess.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Siepe.Core.Entities\Siepe.Core.Entities\Siepe.Core.Entities.csproj" />
    <ProjectReference Include="..\..\..\Microservices\Instruments\Instruments.Entities\Instruments.Entities.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Siepe.Core.Entities.Logging">
      <HintPath>..\..\..\Libraries\Siepe.Core.Entities.Logging\bin\Debug\net6.0\Siepe.Core.Entities.Logging.dll</HintPath>
    </Reference>
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
