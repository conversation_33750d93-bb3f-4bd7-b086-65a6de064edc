﻿using Siepe.Allocations.Entities;
using Siepe.Expenses.Biz;
using Siepe.Expenses.Engine;
using Siepe.Expenses.Entities;
using Siepe.GenevaUtility.Entities;
using Siepe.Shared.DBUtility;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors
{
    public interface IFeeHandler
    {
        void CalcFees(long executionId);
    }

    public class FeeHandler : IFeeHandler
    {
        private readonly ISqlDbAccess sqlDbAccess;
        private readonly IAllocationProvider allocationProvider;
        private readonly  IFeeDataProvider _feeProvider;
        private readonly ICalculationEngine _calcEngine;

        public FeeHandler(ISqlDbAccess sqlDbAccess, IAllocationProvider allocationProvider, 
            IFeeDataProvider _feeProvider,ICalculationEngine _calcEngine )
        {
            this.sqlDbAccess = sqlDbAccess;
            this.allocationProvider = allocationProvider;
            this._feeProvider = _feeProvider;
            this._calcEngine = _calcEngine;
        }

        public void CalcFees(long executionId)
        {
            var rawTrade = sqlDbAccess.GetFromXml<RawTrade>("AIM.pRawTradeXML", new[] { new SqlParameter("@DailyExecutionID", executionId) });
            HandleTradeFees(executionId, rawTrade);
        }


        private void HandleTradeFees(long executionId, RawTrade rawTrade)
        {
            Trace.TraceInformation("[BBG RAW] Calculating trade fees.");
            Allocation allocation = allocationProvider.BuildAllocation(rawTrade);
            List<AppliedExpense> expenses = GetExpenses(allocation);
            if (expenses.Count > 0)
            {
                Trace.TraceInformation("[BBG RAW] Saving trade fees to database.");
                _feeProvider.SaveExpenses(executionId, expenses);
            }
            else
            {
                Trace.TraceInformation("[BBG RAW] No trade fees exist for this trade.");
            }
        }

        private List<AppliedExpense> GetExpenses(Allocation allocation)
        {
            List<AppliedExpense> allExpenses = new List<AppliedExpense>();
            List<AppliedExpense> executionExpenses = _calcEngine.CalculateExpenses(allocation.Execution);
            List<AppliedExpense> allocationExpenses = _calcEngine.CalculateExpenses(allocation);
            allExpenses.AddRange(executionExpenses);
            allExpenses.AddRange(allocationExpenses);
            return allExpenses;
        }
    }
}
