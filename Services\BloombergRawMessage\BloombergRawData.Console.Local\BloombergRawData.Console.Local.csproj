<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <AssemblyTitle>BloombergRawData.Console.Local</AssemblyTitle>
    <Product>BloombergRawData.Console.Local</Product>
    <Copyright>Copyright ©  2017</Copyright>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="CommonServiceLocator" Version="1.3" />
    <PackageReference Include="EnterpriseLibrary.Caching" Version="5.0.505.0" />
    <PackageReference Include="EnterpriseLibrary.Common" Version="6.0.1304.0" />
    <PackageReference Include="Siepe.Services.PublishSubscribe.Assemblies" Version="4.5.20170918.3" />
    <PackageReference Include="Siepe.Shared.Service.Common" Version="4.5.1" />
    <PackageReference Include="Siepe.Shared.Utils" Version="4.0.2" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="7.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="System.Security.AccessControl" Version="6.0.0" />
    <PackageReference Include="System.Security.Permissions" Version="7.0.0" />
    <PackageReference Include="System.Security.Principal.Windows" Version="5.0.0" />
    <PackageReference Include="Unity" Version="3.5.1404.0" />
    <PackageReference Include="Unity.Interception" Version="3.5.1404.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Net" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Activation" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Net.Http" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="TestInput.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\Allocations\Allocations.Entities\Allocations.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Allocations\Orders.Entities\Orders.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Commissions\Entities\Commissions.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility\DBUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Biz\Expenses.Biz.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Engine\Expenses.Engine.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Entities\Expenses.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GeneralUtility\GeneralUtility\GeneralUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility.Entities\GenevaUtility.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility\GenevaUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Data\Siepe.Instruments.Data.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Entities\Siepe.Instruments.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\IssuerCreation\IssuerCreation\IssuerCreation.csproj" />
    <ProjectReference Include="..\..\..\Libraries\OpenFigiApiClient\OpenFigiApiClient.Entities\OpenFigiApiClient.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\OpenFigiApiClient\OpenFigiApiClient\OpenFigiApiClient.csproj" />
    <ProjectReference Include="..\..\..\Libraries\PubSubUtility\PubSubUtility\PubSubUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\ReferenceDataMasterService\BloombergRefDataProvider\BloombergRefDataProvider.csproj" />
    <ProjectReference Include="..\..\..\Libraries\ReferenceDataMasterService\OpenFigiDataProvider\OpenFigiDataProvider.csproj" />
    <ProjectReference Include="..\..\..\Libraries\ReferenceDataMasterService\ReferenceDataMasterService.Data\ReferenceDataMasterService.Data.csproj" />
    <ProjectReference Include="..\..\..\Libraries\ReferenceDataMasterService\ReferenceDataMasterService\ReferenceDataMasterService.csproj" />
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.DataLayer\Siepe.RulesEngine.DataLayer.csproj" />
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.Domain\Siepe.RulesEngine.Domain.csproj" />
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.IronPython\Siepe.RulesEngine.IronPython.csproj" />
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.Services\Siepe.RulesEngine.Services.csproj" />
    <ProjectReference Include="..\..\..\Libraries\UnityUtility\UnityUtility\UnityUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\WCFUnityUtility\WCFUnityUtility\WCFUnityUtility.csproj" />
    <ProjectReference Include="..\Contract\Contract.csproj" />
    <ProjectReference Include="..\Impl\Impl.csproj" />
    <ProjectReference Include="..\Web\_Web.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="Connected Services\SvcBloomergRawData\Reference.cs" />
  </ItemGroup>
</Project>