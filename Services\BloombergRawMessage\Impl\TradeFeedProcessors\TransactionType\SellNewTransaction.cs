﻿using Siepe.GenevaUtility.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class SellNewTransaction : BaseTransaction<SellNewType>, ITransactionType
    {
        public void ProcessTrade(Trade trade, TradeFeedConfiguration config)
        {
            GenevaLoader loader = new GenevaLoader();
            loader.TransactionRecords = new GenevaLoaderTransactionRecords();
            SellNewType transaction = BuildTransactionRecord(trade, config);
            transaction.InvestmentClosingRule = "";
            transaction.SecondaryInvestmentClosingRule = "";
            transaction.CloseLeg = "";
            transaction.MatchFundFinancingSettle = "";
            loader.TransactionRecords.Sell_New = new[] { transaction };
            SendCreateMessage(loader);
        }

        public void CancelTrade(string transactionId, Trade trade, TradeFeedConfiguration config)
        {
            SellDeleteType deleteType = new SellDeleteType()
            {
                KeyValue = new TransactionRecordsKeyValueType() { Value = transactionId }
            };
            GenevaLoader loader = new GenevaLoader()
            {
                TransactionRecords = new GenevaLoaderTransactionRecords()
                {
                    Sell_Delete = new[] { deleteType }
                }
            };
        }
    }
}
