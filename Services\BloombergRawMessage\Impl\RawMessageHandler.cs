﻿using Microsoft.Practices.Unity;
using Siepe.GenevaUtility.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Shared.DBUtility;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class RawMessageHandler : IRawMessageHandler
    {
        private readonly ITradeFeedHandler tradeFeedHandler;

        public RawMessageHandler(ITradeFeedHandler tradeFeedHandler)
        {
            this.tradeFeedHandler = tradeFeedHandler;
        }

        public string Xml { get; set; }

        public void Process(string xml)
        {
            Xml = xml;
            var trade = DeserializeMessage();
            if (trade != null)
            {
                try
                {
                    tradeFeedHandler.Process(trade);
                }
                catch (Exception ex)
                {
                    Trace.TraceError($"Error Processing trade: {ex.Message}, {ex.StackTrace}");
                }
            }
        }

		public RawTrade GetRawTrade(string xml)
		{
			Xml = xml;
			var trade = DeserializeMessage();

			if (trade != null && !trade.TradeDetails.BloombergFunctions.Contains("B-E"))
			{
				return tradeFeedHandler.GetRawTrade(trade);
			}

			return null;
		}

        private Trade DeserializeMessage()
        {
            Trade trade = DeserializeTrade();
            return trade;
        }

        private Trade DeserializeTrade()
        {
            try
            {
                XmlHelper<Trade> tradeHelper = new XmlHelper<Trade>();
                return tradeHelper.Deserialize(Xml);
            }
            catch (Exception e)
            {
                Trace.TraceInformation($"[BBG RAW] Xml message unable to deserialize to Trade object: {e.Message}");
            }
            return null;
        }

    }
}
