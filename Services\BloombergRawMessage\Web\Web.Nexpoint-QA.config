﻿<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <system.web>
  </system.web>
  <system.serviceModel>
    <client xdt:Transform="Replace">
      <endpoint address= "net.msmq://localhost/private/Logging/SvcLogging.svc" binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" behaviorConfiguration="ImpersonationBehavior" contract="IServiceLoggingEntryOnly" name="Logging_netMsmqBinding" />
      <endpoint address= "net.msmq://localhost/private/PublishSubscribe/ServicePublication.svc" binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServicePublication.IServicePublication" name="Publication" />
      <endpoint address="net.tcp://localhost/Configuration/ServiceConfiguration.svc" binding="netTcpBinding" bindingConfiguration="netTcpBindingNone" behaviorConfiguration="ImpersonationBehavior" contract="Siepe.Shared.Service.Configuration.Adapter.IServiceConfiguration" name="Configuration">
        <identity>
          <userPrincipalName value="<EMAIL>" />
        </identity>
      </endpoint>
      <endpoint address="net.tcp://localhost/InstrumentEditorSiepe/ServiceInstrumentEditor.svc" binding="netTcpBinding" bindingConfiguration="netTcpBindingNone" behaviorConfiguration="ImpersonationBehavior" contract="ServiceInstrumentEditor.IServiceInstrumentEditor" name="InstrumentEditor">
        <identity>
          <userPrincipalName value="<EMAIL>"/>
        </identity>
      </endpoint>
      <endpoint address="net.tcp://localhost/LegalEntitySiepe/ServiceLegalEntity.svc/LegalEntity" binding="netTcpBinding" bindingConfiguration="netTcpBindingNone" behaviorConfiguration="ImpersonationBehavior" contract="ServiceLegalEntity.IServiceLegalEntity" name="LegalEntity">
        <identity>
          <userPrincipalName value="<EMAIL>"/>
        </identity>
      </endpoint>
    </client>
    <services xdt:Transform="Replace">
      <service name="Siepe.Service.BloombergRawMessage.Web.ServiceRawMessage" behaviorConfiguration="MEX Enabled">
        <endpoint binding="wsHttpBinding" bindingConfiguration="ReliableTransactionalHTTP" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
        <endpoint binding="netTcpBinding" bindingConfiguration="netTcpBinding" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
        <endpoint binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
        <endpoint binding="mexHttpBinding" contract="IMetadataExchange" address="mex" />
      </service>
      <service name="Siepe.Service.BloombergRawMessage.Web.ServiceRawTrade" behaviorConfiguration="MEX Enabled">
        <endpoint binding="wsHttpBinding" bindingConfiguration="ReliableTransactionalHTTP" contract="Siepe.Service.BloombergRawMessage.Contract.IServiceRawTrade" />
      </service>
    </services>
  </system.serviceModel>
</configuration>
