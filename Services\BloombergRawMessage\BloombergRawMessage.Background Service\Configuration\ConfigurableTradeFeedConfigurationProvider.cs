using Microsoft.Extensions.Options;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Shared.DBUtility;
using System.Data.SqlClient;

namespace Siepe.Service.BloombergRawMessage.BackgroundWorker.Configuration
{
    /// <summary>
    /// .NET Core compatible version of TradeFeedConfigurationProvider that uses IOptions instead of ConfigurationManager
    /// </summary>
    public class ConfigurableTradeFeedConfigurationProvider : ITradeFeedConfigurationProvider
    {
        private readonly ISqlDbAccess _sqlDbAccess;
        private readonly BloombergRawMessageSettings _settings;

        public ConfigurableTradeFeedConfigurationProvider(
            ISqlDbAccess sqlDbAccess, 
            IOptions<BloombergRawMessageSettings> settings)
        {
            _sqlDbAccess = sqlDbAccess ?? throw new ArgumentNullException(nameof(sqlDbAccess));
            _settings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
        }

        public TradeFeedConfiguration GetConfiguration()
        {
            string config = _settings.TransactionConfig;
            
            if (string.IsNullOrWhiteSpace(config))
            {
                throw new InvalidOperationException("TransactionConfig setting is required but not configured.");
            }

            return _sqlDbAccess.GetFromXml<TradeFeedConfiguration>(
                "Enterprise.dbo.pConfigurationSectionRawXml",
                new[] { new SqlParameter("@configurationName", config) });
        }
    }
}
