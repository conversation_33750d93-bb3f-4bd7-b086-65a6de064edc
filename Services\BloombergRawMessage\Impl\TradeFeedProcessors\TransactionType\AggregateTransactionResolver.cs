﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class AggregateTransactionResolver : ITransactionResolver
    {
        public Dictionary<string, ITransactionType> _transactionTypes { get; set; }

        public AggregateTransactionResolver(ITransactionType[] transactionTypes)
        {
            _transactionTypes = transactionTypes.ToDictionary(type => type.GetType().Name);
        }

        public ITransactionType Resolve(string typeName)
        {
            if (!string.IsNullOrWhiteSpace(typeName) && _transactionTypes.ContainsKey(typeName))
            {
                return _transactionTypes[typeName];
            }
            return null;
        }
    }
}
