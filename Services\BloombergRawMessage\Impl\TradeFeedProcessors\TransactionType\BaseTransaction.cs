﻿using Microsoft.Practices.Unity;
using Siepe.GenevaUtility;
using Siepe.GenevaUtility.Entities;
using Siepe.Instruments.Data;
using Siepe.Instruments.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class BaseTransaction<T> where T : TransactionRecords, new()
    {
        [Dependency]
        public IInstrumentDataProvider _instrumentProvider { get; set; }

        [Dependency]
        public IGenevaDataProvider _dataProvider { get; set; }

        [Dependency]
        public IInstTypeResolver _typeResolver { get; set; }

        public virtual T BuildTransactionRecord(Trade trade, TradeFeedConfiguration config)
        {
            IInstrumentTypeHandler handler = GetInstrumentTypeHandler(trade);
            T transaction = new T();
            GenevaLoader portfolio = GetPortfolioObject(trade.TradeDetails.TraderAccountName);
            transaction.Portfolio = GetPortfolioName(portfolio);
            transaction.LocationAccount = GetLocationAccount(portfolio);
            transaction.Investment = GetConfigurationValue("InvestmentPrefix", config) + handler.GetInvestment(trade.TradeDetails.UniqueBloombergID);
            transaction.Quantity = handler.GetQuantity(trade);
            transaction.Price = trade.TradeDetails.ExtendedPrecisionPrice;
            transaction.NetInvestmentAmount = GetConfigurationValue("NetInvestmentAmount", config);
            transaction.TotCommission = GetCommission(trade.TransactionCosts);
            transaction.EventDate = ParseDate(trade.TradeDetails.AsOfTradeDate);
            transaction.SettleDate = trade.TradeDetails.SettlementDate ?? "";
            transaction.ActualSettleDate = trade.TradeDetails.SettlementDate ?? "";
            transaction.UserDefField1 = trade.TradeDetails.OMSOrderNumber ?? "";
            transaction.UserTranId1 = GetConfigurationValue("TicketPrefix", config) + trade.TradeDetails.OriginalTktId;
            transaction.UserTranId2 = trade.TradeDetails.BloombergReferenceNumber;
            transaction.PriceDenomination = trade.TradeDetails.SecurityCurrencyISOCode; //trade.TransactionCosts?.FirstOrDefault()?.Currency ?? trade.TradeDetails.SecurityCurrencyISOCode ?? trade.TradeDetails.SettlementCurrencyISOCode;
            transaction.CounterInvestment = trade.TradeDetails.SettlementCurrencyISOCode; //transaction.PriceDenomination;
            transaction.Broker = trade.TradeDetails.CustomerAccountCounterparty ?? trade.TradeDetails.AccountCounterpartyShortName ?? trade.TradeDetails.MasterAccountName;
            transaction.Trader = trade.TradeDetails.SalespersonLogin;
            transaction.NetCounterAmount = GetConfigurationValue("NetCounterAmount", config);
            transaction.CounterFXDenomination = GetConfigurationValue("CounterFXDenomination", config);
            transaction.NetTradeFlag = GetConfigurationValue("NetTradeFlag", config);
            transaction.AccrueCommissionFlag = GetConfigurationValue("AccrueCommissionFlag", config);
            transaction.PrimaryExchangeMIC = GetPrimaryExchange(trade);
            transaction.tradeFX = trade.TradeDetails.SettlementCurrencyRate;//GetConfigurationValue("TradeFX");
            transaction.GrossAmountLocal = trade.TradeDetails.RedemptionCcyPrincipal;
            transaction.GrossAmountRC = trade.TradeDetails.FundCcyPrincipal;
            transaction.NetAmountLocal = trade.TradeDetails.TotalTradeAmount;
            transaction.NetAmountSettled = trade.TradeDetails.SettlementAmount;
            transaction.ContractualSettleDate = trade.TradeDetails.WorkoutDate ?? "";
            transaction.SideCode = trade.TradeDetails.BuySellCoverShortFlag;
            transaction.Comments = ""; //hardcoded - see about getting this from config
            transaction.TradeFlatFlag = trade.TradeDetails.TradeFlatFlag;
            //transaction.AccruedInterest
            //transaction.CfdSwapFlag
            //transaction.MortgageFactor
            return transaction;
        }

        public virtual void SendCreateMessage(GenevaLoader loader)
        {
            try
            {

            }
            catch (Exception e)
            {

            }
        }

        public virtual void SendCancelMessage(GenevaLoader loader)
        {
            try
            {

            }
            catch (Exception e)
            {

            }
        }

        #region helper

        private IInstrumentTypeHandler GetInstrumentTypeHandler(Trade trade)
        {
            IInstrumentTypeHandler handler = null;
            Instrument instrument = _instrumentProvider.GetInstrumentByBloombergID(trade.TradeDetails.UniqueBloombergID);
            handler = _typeResolver.Resolve(instrument?.InstrumentType?.GenevaInvestmentType);
            return handler;
        }

        private GenevaLoader GetPortfolioObject(string portfolio)
        {
            return _dataProvider.GetPortfolioXmlByBloombergID(portfolio);
        }

        private string GetPortfolioName(GenevaLoader loader)
        {
            return loader?.PartyRecords?.LocationAccount_InsertUpdate?.NameLine1?.Trim() ?? "";
        }

        private string GetLocationAccount(GenevaLoader loader)
        {
            return loader?.PartyRecords?.LocationAccount_InsertUpdate?.LocationAccount?.Trim() ?? "";
        }

        private string GetPrimaryExchange(Trade trade)
        {
            return trade.TradeDetails.ShortNotes.FirstOrDefault(note => note.Index == 5)?.Text?.Trim() ?? string.Empty;
        }

        private string GetConfigurationValue(string key, TradeFeedConfiguration config)
        {
            if (config?.FieldMappings != null && config.FieldMappings.Any(m => m.Key.Equals(key)))
            {
                return config.FieldMappings.First(m => m.Key.Equals(key)).Value;
            }
            return "";
        }

        private double? GetCommission(List<TransactionCost> transactionCosts)
        {
            double? commission = null;
            if (transactionCosts.Any(cost => cost.Type.Equals("2")))
            {
                commission = Convert.ToDouble(transactionCosts.First(cost => cost.Type.Equals("2")).Cost);
            }
            return commission;
        }

        private DateTime? ParseDate(string dateStr)
        {
            if (!string.IsNullOrWhiteSpace(dateStr))
            {
                if (DateTime.TryParseExact(dateStr, "yyyy-MM-dd", null, DateTimeStyles.None, out DateTime d))
                {
                    return d;
                }
                else if (DateTime.TryParse(dateStr, out DateTime r))
                {
                    return r.Date;
                }
            }
            return null;
        }

        #endregion
    }
}
