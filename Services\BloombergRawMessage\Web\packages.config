﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="CommonServiceLocator" version="1.3" targetFramework="net45" />
  <package id="EnterpriseLibrary.Caching" version="5.0.505.0" targetFramework="net45" />
  <package id="EnterpriseLibrary.Common" version="6.0.1304.0" targetFramework="net472" />
  <package id="NuGetEnablePackageRestore" version="2.0.0" targetFramework="net45" />
  <package id="OctoPack" version="2.0.26" targetFramework="net45" />
  <package id="Siepe.Services.PublishSubscribe.Assemblies" version="4.5.20160825.1" targetFramework="net45" />
  <package id="Siepe.Shared.Service.Common" version="4.5.1" targetFramework="net472" />
  <package id="Siepe.Shared.Service.Configuration.Assemblies" version="4.5.14353.1" targetFramework="net45" />
  <package id="Siepe.Shared.Utils" version="4.0.2" targetFramework="net472" />
  <package id="System.Configuration.ConfigurationManager" version="7.0.0" targetFramework="net472" />
  <package id="System.Data.SqlClient" version="4.8.6" targetFramework="net472" />
  <package id="System.Security.AccessControl" version="6.0.0" targetFramework="net472" />
  <package id="System.Security.Permissions" version="7.0.0" targetFramework="net472" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net472" />
  <package id="Unity" version="3.5.1404.0" targetFramework="net45" />
  <package id="Unity.Interception" version="3.5.1404.0" targetFramework="net45" />
</packages>