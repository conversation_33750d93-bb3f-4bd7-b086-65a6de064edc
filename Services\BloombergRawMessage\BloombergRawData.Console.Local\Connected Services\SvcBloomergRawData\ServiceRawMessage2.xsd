<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/Siepe.Shared.Service.PublishSubscribe.Contract.Dto" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/Siepe.Shared.Service.PublishSubscribe.Contract.Dto" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="Message">
    <xs:sequence>
      <xs:element minOccurs="0" name="BaseSubject" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CreatedDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedUser" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ID" type="xs:long" />
      <xs:element minOccurs="0" name="Payload" nillable="true" type="xs:base64Binary" />
      <xs:element minOccurs="0" name="Subject" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Subscription" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Title" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Message" nillable="true" type="tns:Message" />
</xs:schema>