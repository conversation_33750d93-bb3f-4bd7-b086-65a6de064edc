﻿using Siepe.GenevaUtility;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siepe.GenevaUtility.Entities;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class StifHandler : BaseInstrumentTypeHandler, IInstrumentTypeHandler
    {
        public override InvestmentInsertUpdateType GetInvestmentRecord(GenevaLoader loader)
        {
            return loader?.InvestmentRecords?.Stif_InsertUpdate?.FirstOrDefault();
        }
    }
}
