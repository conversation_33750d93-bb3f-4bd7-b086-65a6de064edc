﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <connectionStrings>
    <add name="CoreConnectionString" connectionString="Data Source=005sql05.highland.aws,52155;Initial Catalog=HCM;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />
    <!--<add name="FeedsConnectionString" connectionString="Data Source=005sql05.highland.aws,52155;Initial Catalog=DataFeeds;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />-->
    <add name="FeedsConnectionString" connectionString="Data Source=PHCMDB01.hcmlp.com;Initial Catalog=DataFeeds;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
  </startup>
  <system.serviceModel>
    <bindings>
      <netTcpBinding>
        <binding name="NetTcpBinding_IServicePublication">
          <security>
            <transport sslProtocols="None" />
          </security>
        </binding>
      </netTcpBinding>
      <wsHttpBinding>
        <binding name="WSHttpBinding_IServicePublication">
          <reliableSession enabled="true" />
        </binding>
      </wsHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc"
          binding="wsHttpBinding" bindingConfiguration="WSHttpBinding_IServicePublication"
          contract="BBGRawSvc.IServicePublication" name="WSHttpBinding_IServicePublication">
        <identity>
          <userPrincipalName value="<EMAIL>" />
        </identity>
      </endpoint>
      <endpoint address="net.tcp://005s03.highland.aws/BloombergRawData/ServiceRawMessage.svc"
          binding="netTcpBinding" bindingConfiguration="NetTcpBinding_IServicePublication"
          contract="BBGRawSvc.IServicePublication" name="NetTcpBinding_IServicePublication">
        <identity>
          <userPrincipalName value="005.Svc.Portalhighland.aws" />
        </identity>
      </endpoint>
    </client>
  </system.serviceModel>
</configuration>