using System;
using System.Linq;
using System.ServiceModel;
using Siepe.Shared.Service.Common.ServiceModelEx.Errors;
using Siepe.Shared.Service.Common.Contract.Fault;
using Siepe.Shared.Service.PublishSubscribe.Contract;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Service.BloombergRawMessage.Impl;
using System.Configuration;
using Siepe.Shared.Service.PublishSubscribe.Contract.Dto;
using System.Text;
using System.Diagnostics;
using System.Collections.Generic;
using Siepe.GenevaUtility.Entities;

namespace Siepe.Service.BloombergRawMessage.Web
{
    [ErrorHandlerBehavior]
    [ServiceBehavior(InstanceContextMode = InstanceContextMode.PerCall, IncludeExceptionDetailInFaults = true)]
    public class ServiceRawTrade : IServiceRawTrade
	{
        private readonly IRawMessageHandler _handler;

        public ServiceRawTrade(IRawMessageHandler handler)
        {
            _handler = handler;
        }

        public List<RawTrade> GetRawTrades(List<string> tradeFeed)
        {
			return tradeFeed.Select(x => _handler.GetRawTrade(x)).ToList();
        }
    }
}
