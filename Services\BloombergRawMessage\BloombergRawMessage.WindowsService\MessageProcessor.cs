using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Shared.Service.PublishSubscribe.Contract.Dto;
using System;
using System.Configuration;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.WindowsService
{
    public class MessageProcessor
    {
        private readonly IRawMessageHandler _handler;

        public MessageProcessor(IRawMessageHandler handler)
        {
            _handler = handler ?? throw new ArgumentNullException(nameof(handler));
        }

        /// <summary>
        /// Processes a message received from RabbitMQ or other transport.
        /// This mimics the limited scope Publish method from ServiceRawMessage.
        /// Excludes Calc.Execution.Fees handling as per requirements.
        /// </summary>
        /// <param name="message">The message to process</param>
        public void ProcessMessage(Message message)
        {
            try
            {
                if (message == null)
                {
                    Trace.TraceWarning("[BBG RAW Service] Received null message, skipping processing.");
                    return;
                }

                // Skip Calc.Execution.Fees messages 
                if (message.BaseSubject == "Calc.Execution.Fees")
                {
                    Trace.TraceInformation($"[BBG RAW Service] Skipping Calc.Execution.Fees message as per service scope limitations.");
                    return;
                }

                // Process only messages with payload (Bloomberg trade data)
                if (message.Payload != null)
                {
                    Trace.TraceInformation($"[BBG RAW Service] Processing message with subject: {message.BaseSubject}");
                    
                    string xml = Encoding.UTF8.GetString(message.Payload);
                    
                    if (string.IsNullOrWhiteSpace(xml))
                    {
                        Trace.TraceWarning("[BBG RAW Service] Message payload is empty or whitespace, skipping processing.");
                        return;
                    }

                    // Process the XML through the existing handler
                    _handler.Process(xml);
                    
                    Trace.TraceInformation($"[BBG RAW Service] Successfully processed message with subject: {message.BaseSubject}");
                }
                else
                {
                    Trace.TraceWarning($"[BBG RAW Service] Message with subject '{message.BaseSubject}' has no payload, skipping processing.");
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error processing message: {ex.Message}");
                Trace.TraceError($"[BBG RAW Service] Stack trace: {ex.StackTrace}");
                
                // Log message details for debugging (be careful with sensitive data)
                if (message != null)
                {
                    Trace.TraceError($"[BBG RAW Service] Failed message subject: {message.BaseSubject}");
                    Trace.TraceError($"[BBG RAW Service] Failed message payload length: {message.Payload?.Length ?? 0}");
                }
                
                // Re-throw to allow higher-level error handling
                throw;
            }
        }

        /// <summary>
        /// Processes a message asynchronously.
        /// </summary>
        /// <param name="message">The message to process</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ProcessMessageAsync(Message message)
        {
            await Task.Run(() => ProcessMessage(message));
        }

        /// <summary>
        /// Placeholder method for RabbitMQ message subscription.
        /// This will be implemented later with actual RabbitMQ client code.
        /// </summary>
        public void StartRabbitMQListener()
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Starting RabbitMQ listener...");
                
                // TODO: Implement RabbitMQ connection and message subscription
                // This is a placeholder for future implementation
                
                string hostName = ConfigurationManager.AppSettings["RabbitMQ.HostName"] ?? "localhost";
                string port = ConfigurationManager.AppSettings["RabbitMQ.Port"] ?? "5672";
                string userName = ConfigurationManager.AppSettings["RabbitMQ.UserName"] ?? "guest";
                string password = ConfigurationManager.AppSettings["RabbitMQ.Password"] ?? "guest";
                string queueName = ConfigurationManager.AppSettings["RabbitMQ.QueueName"] ?? "bloomberg.raw.messages";
                
                Trace.TraceInformation($"[BBG RAW Service] RabbitMQ Configuration - Host: {hostName}, Port: {port}, Queue: {queueName}");
                Trace.TraceInformation("[BBG RAW Service] RabbitMQ listener placeholder - implementation pending.");
                
                // Future implementation will include:
                // 1. Create RabbitMQ connection factory
                // 2. Create connection and channel
                // 3. Declare queue if needed
                // 4. Set up message consumer
                // 5. Subscribe to messages and call ProcessMessage for each received message
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error starting RabbitMQ listener: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Placeholder method for stopping RabbitMQ message subscription.
        /// This will be implemented later with actual RabbitMQ client code.
        /// </summary>
        public void StopRabbitMQListener()
        {
            try
            {
                Trace.TraceInformation("[BBG RAW Service] Stopping RabbitMQ listener...");
                
                // TODO: Implement RabbitMQ connection cleanup
                // This is a placeholder for future implementation
                
                Trace.TraceInformation("[BBG RAW Service] RabbitMQ listener stopped (placeholder).");
                
                // Future implementation will include:
                // 1. Close RabbitMQ channel
                // 2. Close RabbitMQ connection
                // 3. Dispose resources
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error stopping RabbitMQ listener: {ex.Message}");
                throw;
            }
        }
    }
}
