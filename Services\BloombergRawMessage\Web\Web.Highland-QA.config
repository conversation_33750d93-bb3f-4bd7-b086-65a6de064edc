<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <system.web>
  </system.web>
  <system.serviceModel>
    <client xdt:Transform="Replace">
      <endpoint address= "net.msmq://qaservices.highland.aws/private/Logging/SvcLogging.svc"
                binding="netMsmqBinding"
                bindingConfiguration="netMsmqBinding"
                behaviorConfiguration="ImpersonationBehavior"
                contract="IServiceLoggingEntryOnly"
                name="Logging_netMsmqBinding">
      </endpoint>
      <endpoint address= "net.msmq://qaservices.highland.aws/private/PublishSubscribeSiepe/ServicePublication.svc"
                binding="netMsmqBinding"
                bindingConfiguration="netMsmqBinding"
                behaviorConfiguration="ImpersonationBehavior"
                contract="Siepe.Shared.Service.PublishSubscribe.Adapter.IServicePublication"
                name="Publication">
      </endpoint>
      <endpoint address="net.tcp://qaservices.highland.aws/ConfigurationSiepe/ServiceConfiguration.svc"
                binding="netTcpBinding"
                bindingConfiguration="netTcpBinding"
                behaviorConfiguration="ImpersonationBehavior"
                contract="Siepe.Shared.Service.Configuration.Adapter.IServiceConfiguration"
                name="Configuration">
        <identity>
          <userPrincipalName value="<EMAIL>" />
        </identity>
      </endpoint>
      <endpoint address="net.tcp://qaservices.highland.aws/InstrumentEditorSiepe/ServiceInstrumentEditor.svc"
               binding="netTcpBinding"
               bindingConfiguration="netTcpBinding"
               behaviorConfiguration="ImpersonationBehavior"
               contract="ServiceInstrumentEditor.IServiceInstrumentEditor"
               name="InstrumentEditor">
        <identity>
          <userPrincipalName value="<EMAIL>"/>
        </identity>
      </endpoint>
      <endpoint address="net.tcp://qaservices.highland.aws/LegalEntitySiepe/ServiceLegalEntity.svc/LegalEntity"
              binding="netTcpBinding"
              bindingConfiguration="netTcpBinding"
              behaviorConfiguration="ImpersonationBehavior"
              contract="ServiceLegalEntity.IServiceLegalEntity"
              name="LegalEntity">
        <identity>
          <userPrincipalName value="<EMAIL>"/>
        </identity>
      </endpoint>
     
    </client>
  </system.serviceModel>
</configuration>
