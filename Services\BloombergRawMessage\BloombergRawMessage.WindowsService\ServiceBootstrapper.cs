using Microsoft.Practices.Unity;
using Siepe.Expenses.Biz;
using Siepe.GenevaUtility;
using Siepe.Instruments.Data;
using Siepe.PubSubUtility;
using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;

using Siepe.Shared.UnityUtility;
using System;
using System.Diagnostics;
using Siepe.Shared.DBUtility.v1;
using Siepe.Expenses.Engine;
using Siepe.RulesEngine.Services;
using Siepe.RulesEngine.IronPython;
using Siepe.RulesEngine.DataLayer;
using Siepe.Shared.DBUtility;
using Siepe.Serialization;
using Siepe.Serialization.JsonSerializer;
using Siepe.IssuerCreation;

namespace Siepe.Service.BloombergRawMessage.WindowsService
{
    public class ServiceBootstrapper
    {
        public void ConfigureContainer(IUnityContainer container)
        {
            try
            {
                //Trace.TraceInformation("[BBG RAW Service] Configuring Unity container...");

                container.RegisterType<ISqlDbAccess, Shared.DBUtility.SqlDbAccess>(new InjectionConstructor(new InjectionParameter("CoreConnectionString")));
                container.RegisterType<IDbAccess, Shared.DBUtility.v1.SqlDbAccess>(new InjectionConstructor(new InjectionParameter("CoreConnectionString")));

                // Database connections
                container.RegisterType<IGenevaDataProvider, GenevaDataProvider>(new InjectionConstructor(new InjectionParameter("FeedsConnectionString"), new ResolvedParameter<ISqlDbAccess>("Feeds")))
                    .RegisterType<IGenevaServiceProvider, GenevaServiceProvider>()
                    .RegisterType<ISqlDbAccess, Shared.DBUtility.SqlDbAccess>(new InjectionConstructor(new InjectionParameter("CoreConnectionString")))
                    .RegisterType<ISqlDbAccess, Shared.DBUtility.SqlDbAccess>("Feeds", new InjectionConstructor(new InjectionParameter("FeedsConnectionString")));
                
                // Pub/Sub
                container.RegisterType<IPublicationService, PublicationService>();

                container.RegisterType<IIssuerCreator, IssuerCreator>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()));


                // Expenses
                container.RegisterType<IFeeDataProvider, FeeDataProvider>(new InjectionConstructor(new ResolvedParameter<IDbAccess>()));

                container.RegisterType<ICalculationEngine, CalculationEngine>();
                container.RegisterType<IRulesEngineSvc, RulesEngineSvc>();
                container.RegisterType<IRulesEngine, IronPythonRulesEngine>();
                container.RegisterType<IRulesRepository, DbDataRepository>();
                container.RegisterType<IRulesRepository, SqlDbAccessDataRepository>();
                container.RegisterType<ITransactionResolver, AggregateTransactionResolver>();

                // Handlers
                container
                    .RegisterType<ITradeFeedConfigurationProvider, TradeFeedConfigurationProvider>(new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()))
                    .RegisterType<IAllocationProvider, AllocationProvider>()
                    .RegisterType<ITradeFeedHandler, TradeFeedHandler>()
                    .RegisterType<ISerializer, JsonSerializer>();


                container
                    .RegisterType<IInstrumentProvider, FutureInstrumentProvider>(new InjectionConstructor(new ResolvedParameter<IDbAccess>()))
                    .RegisterType<IInstrumentDataProvider, InstrumentDataProvider>(new InjectionConstructor(new ResolvedParameter<IDbAccess>()));

                container.RegisterType<IInstTypeResolver, AggregateInstTypeResolver>();
                container.ForAssembly(typeof(IInstrumentTypeHandler).Assembly, (a) =>
                {
                    a.RegisterInterfaces<IInstrumentTypeHandler>();
                });

                // Implementation
                container.RegisterType<IRawMessageHandler, RawMessageHandler>();

                container
                    .RegisterType<IRawTradeProcessorProvider, RawTradeProcessorProvider>()
                    .RegisterType<IRawTradeProcessor, CfdSwapProcessor>(nameof(CfdSwapProcessor),
                        new ContainerControlledLifetimeManager(), new InjectionConstructor(new ResolvedParameter<ISqlDbAccess>()));
                
                // Message processor
                container.RegisterType<MessageProcessor>();
                var tradeFeedHandler = container.Resolve<ITradeFeedHandler>();
               //Trace.TraceInformation("[BBG RAW Service] Unity container configuration completed successfully.");
            }
            catch (Exception ex)
            {
                Trace.TraceError($"[BBG RAW Service] Error configuring Unity container: {ex.Message}");
                Trace.TraceError($"[BBG RAW Service] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
