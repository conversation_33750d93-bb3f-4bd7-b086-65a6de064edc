namespace Siepe.Service.BloombergRawMessage.BackgroundWorker.Configuration
{
    /// <summary>
    /// Interface for providing service information (replaces Web service's IServiceCommon functionality)
    /// </summary>
    public interface IServiceInfoProvider
    {
        /// <summary>
        /// Gets the domain/environment name
        /// </summary>
        string GetDomainName();

        /// <summary>
        /// Gets the service version
        /// </summary>
        string GetVersion();

        /// <summary>
        /// Gets the service name
        /// </summary>
        string GetServiceName();

        /// <summary>
        /// Gets the company name
        /// </summary>
        string GetCompany();

        /// <summary>
        /// Gets the location
        /// </summary>
        string GetLocation();

        /// <summary>
        /// Gets the default issuer
        /// </summary>
        string GetDefaultIssuer();

        /// <summary>
        /// Gets whether security master processing is enabled
        /// </summary>
        bool GetProcessSecMaster();
    }
}
