<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net472</TargetFramework>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
    <ReferencePath>$(ProgramFiles)\Common Files\microsoft shared\VSTT\$(VisualStudioVersion)\UITestExtensionPackages</ReferencePath>
    <IsCodedUITest>False</IsCodedUITest>
    <TestProjectType>UnitTest</TestProjectType>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <AssemblyTitle>Test</AssemblyTitle>
    <Product>Test</Product>
    <Copyright>Copyright ©  2017</Copyright>
    <OutputPath>bin\$(Configuration)\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <Import Project="$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets" Condition="Exists('$(VSToolsPath)\TeamTest\Microsoft.TestTools.targets')" />
  <ItemGroup>
    <PackageReference Include="CommonServiceLocator" Version="1.3" />
    <PackageReference Include="EnterpriseLibrary.Caching" Version="5.0.505.0" />
    <PackageReference Include="EnterpriseLibrary.Common" Version="6.0.1304.0" />
    <PackageReference Include="RhinoMocks" Version="3.6.1" />
    <PackageReference Include="Siepe.Shared.Service.Common" Version="4.5.1" />
    <PackageReference Include="Siepe.Shared.Service.Configuration.Assemblies" Version="4.5.14353.1" />
    <PackageReference Include="Siepe.Shared.Utils" Version="4.0.2" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
    <PackageReference Include="Unity" Version="3.5.1404.0" />
    <PackageReference Include="Unity.Interception" Version="3.5.1404.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.1.1" />
    <PackageReference Include="MSTest.TestFramework" Version="3.1.1" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="TradeFeedTest\testXml\BuyDelete.xml" />
    <Content Include="TradeFeedTest\testXml\BuyDelete2.xml" />
    <Content Include="TradeFeedTest\testXml\BuyNew.xml" />
    <Content Include="TradeFeedTest\testXml\BuyNew2.xml" />
    <Content Include="TradeFeedTest\testXml\SellDelete.xml" />
    <Content Include="TradeFeedTest\testXml\SellNew.xml" />
    <Content Include="TradeFeedTest\testXml\Test.xml">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="TradeFeedTest\testXml\WSO.xml" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\Allocations\Allocations.Entities\Allocations.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Allocations\Orders.Entities\Orders.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility\DBUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Biz\Expenses.Biz.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Engine\Expenses.Engine.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Entities\Expenses.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility.Entities\GenevaUtility.Entities.csproj" />
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility\GenevaUtility.csproj" />
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Data\Siepe.Instruments.Data.csproj" />
    <ProjectReference Include="..\..\..\Libraries\PubSubUtility\PubSubUtility\PubSubUtility.csproj" />
    <ProjectReference Include="..\Contract\Contract.csproj" />
    <ProjectReference Include="..\Impl\Impl.csproj" />
  </ItemGroup>
</Project>