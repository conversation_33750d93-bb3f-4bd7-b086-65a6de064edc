﻿using Siepe.Service.BloombergRawMessage.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public interface ITransactionType
    {
        void ProcessTrade(Trade trade, TradeFeedConfiguration config);

        void CancelTrade(string transactionId, Trade trade, TradeFeedConfiguration config);
    }
}
