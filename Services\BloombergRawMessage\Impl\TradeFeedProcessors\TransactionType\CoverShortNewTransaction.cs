﻿using Siepe.GenevaUtility.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class CoverShortNewTransaction : BaseTransaction<CoverShortNewType>, ITransactionType
    {
        public void ProcessTrade(Trade trade, TradeFeedConfiguration config)
        {
            GenevaLoader loader = new GenevaLoader();
            loader.TransactionRecords = new GenevaLoaderTransactionRecords();
            CoverShortNewType transaction = BuildTransactionRecord(trade, config);
            transaction.InvestmentClosingRule = "";
            transaction.SecondaryInvestmentClosingRule = "";
            transaction.CloseLeg = "";
            loader.TransactionRecords.CoverShort_New = new[] { transaction };
            SendCreateMessage(loader);
        }

        public void CancelTrade(string transactionId, Trade trade, TradeFeedConfiguration config)
        {
            CoverShortDeleteType deleteType = new CoverShortDeleteType()
            {
                KeyValue = new TransactionRecordsKeyValueType() { Value = transactionId }
            };
            GenevaLoader loader = new GenevaLoader()
            {
                TransactionRecords = new GenevaLoaderTransactionRecords()
                {
                    CoverShort_Delete = new[] { deleteType }
                }
            };
            _dataProvider.Cancel(transactionId);
        }
    }
}
