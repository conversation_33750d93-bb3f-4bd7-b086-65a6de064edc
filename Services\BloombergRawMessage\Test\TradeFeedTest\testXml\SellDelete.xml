﻿<!--This is faked from SellNew.xml-->
<TradeFeed xmlns="urn:tradefeed-xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Common>
    <BloombergFirmID>3461</BloombergFirmID>
    <TransactionNumber>2810338</TransactionNumber>
    <SecurityIdentifierFlag>1</SecurityIdentifierFlag>
    <SecurityIdentifier>02376R102</SecurityIdentifier>
    <SecurityCurrencyISOCode>USD</SecurityCurrencyISOCode>
    <SecurityProductKey>2</SecurityProductKey>
    <BloombergIdentifier>02376R102   </BloombergIdentifier>
    <Ticker>AAL</Ticker>
    <SeriesExchangeCode>US</SeriesExchangeCode>
    <BuySellCoverShortFlag>S</BuySellCoverShortFlag>
    <RecordType>102</RecordType>
    <TradeDate>2017-07-18T16:34:10</TradeDate>
    <AsOfTradeDate>2017-07-18T15:59:56</AsOfTradeDate>
    <SettlementDate>2017-07-19</SettlementDate>
    <Price>53.3102</Price>
    <TradeAmount>12529.0</TradeAmount>
    <CustomerAccountCounterparty>MLCO-A</CustomerAccountCounterparty>
    <AccountCounterpartyShortName>MLCO-A</AccountCounterpartyShortName>
    <SettlementLocationIndicator>2</SettlementLocationIndicator>
    <ProductSubFlag>0</ProductSubFlag>
    <PrincipalLoanAmount>667923.5</PrincipalLoanAmount>
    <ShortNotes>
      <ShortNote>
        <Index>2</Index>
        <Text>STST        </Text>
      </ShortNote>
      <ShortNote>
        <Index>3</Index>
        <Text>N           </Text>
      </ShortNote>
      <ShortNote>
        <Index>5</Index>
        <Text>XNGS        </Text>
      </ShortNote>
    </ShortNotes>
    <LongNotes>
      <LongNote>
        <Index>3</Index>
        <Text>USD                                          </Text>
      </LongNote>
      <LongNote>
        <Index>4</Index>
        <Text>02376R102US02376R1023BCV7KT2                 </Text>
      </LongNote>
    </LongNotes>
    <TraderAccountName>HIGASTST</TraderAccountName>
    <TimeOfSlate>12:18:53</TimeOfSlate>
    <TimeOfSalesTicket>16:34:02</TimeOfSalesTicket>
    <LastLogin>MPEARSON20</LastLogin>
    <SalespersonLogin>MPEARSON20</SalespersonLogin>
    <MasterTicketNumber>2810337</MasterTicketNumber>
    <SettlementCurrencyISOCode>USD</SettlementCurrencyISOCode>
    <SettlementCurrencyRate>1.0</SettlementCurrencyRate>
    <CancelDueToCorrection>N</CancelDueToCorrection>
    <InvertFlag>N</InvertFlag>
    <AutoExTradeFlag>N</AutoExTradeFlag>
    <NameOfUserWhoAuthorizedTrade>AUTO-RELEASE</NameOfUserWhoAuthorizedTrade>
    <DateTradeWasAuthorized>2017-07-18</DateTradeWasAuthorized>
    <TraderToTraderTradeFlag>N</TraderToTraderTradeFlag>
    <Version>2000</Version>
    <UniqueBloombergID>******************</UniqueBloombergID>
    <ExtendedPrecisionPrice>53.3102</ExtendedPrecisionPrice>
    <SecondaryTransactionTicketNumber>125054</SecondaryTransactionTicketNumber>
    <SystemDate>2017-07-18</SystemDate>
    <ImpactFlag>true</ImpactFlag>
    <SettlementLocationAbbreviation>DTC</SettlementLocationAbbreviation>
    <TraderAccountNumber>185</TraderAccountNumber>
    <TransactionType>OA  </TransactionType>
    <CustodySafekeepingNumber>not specified</CustodySafekeepingNumber>
    <EnteredTicketUserId>8647333</EnteredTicketUserId>
    <AllocatedTicketUserId>********</AllocatedTicketUserId>
    <BloombergFunctions>
      <FunctionName>TKT </FunctionName>
      <FunctionName>OAX </FunctionName>
      <FunctionName>OA  </FunctionName>
      <FunctionName />
    </BloombergFunctions>
    <OMReasonCode>1</OMReasonCode>
    <OMSOrderNumber>125054</OMSOrderNumber>
    <MSRBReportable>true</MSRBReportable>
    <QuoteTypeIndicator>1</QuoteTypeIndicator>
    <PrimeBroker>STST</PrimeBroker>
    <StrategyTags>
      <StrategyTag>
        <Level>1</Level>
        <ID>3</ID>
        <Name>Jim Dondero</Name>
      </StrategyTag>
    </StrategyTags>
    <SoftDollarFlag>N</SoftDollarFlag>
    <OMSOrderType>0</OMSOrderType>
    <OMSTimeInForce>0</OMSTimeInForce>
    <TotalTradeAmount>667609.88</TotalTradeAmount>
    <MasterAccount>MLCO</MasterAccount>
    <MasterAccountName>MLCO</MasterAccountName>
    <CTMMatchStatus>NR</CTMMatchStatus>
    <MatchDate>2017-07-18T16:34:10</MatchDate>
    <IsDirtyPrice>N</IsDirtyPrice>
    <TSAMIndicator>NR</TSAMIndicator>
    <TaxLotMethod>0</TaxLotMethod>
    <SecurityPrice>53.3102</SecurityPrice>
    <SettlementAmount>667609.88</SettlementAmount>
    <BloombergReferenceNumber>31693461130002810338</BloombergReferenceNumber>
    <FundCcyPrincipal>667923.5</FundCcyPrincipal>
    <FundCcyTotalCommission>-313.62</FundCcyTotalCommission>
    <RedemptionCcyPrincipal>667923.5</RedemptionCcyPrincipal>
    <RedemptionTotalCommission>-313.62</RedemptionTotalCommission>
    <SettlementCcyPrincipal>667923.5</SettlementCcyPrincipal>
    <SettlementCcyTotalCommission>-313.62</SettlementCcyTotalCommission>
    <PrimaryExchangeCode>UW</PrimaryExchangeCode>
    <ExecutionPlatform>-1</ExecutionPlatform>
    <ClientAuth>N</ClientAuth>
    <BasketID>0</BasketID>
    <OMSTransactionID>2</OMSTransactionID>
    <LastLoginUUID>********</LastLoginUUID>
    <OriginalTktId>2810338</OriginalTktId>
    <SettlementCcyPrice>53.3102</SettlementCcyPrice>
    <RTTMIndicator>NONE</RTTMIndicator>
    <RTTMReferenceID>34616H002810338</RTTMReferenceID>
    <CounterpartyEncodedLongName>TWVycmlsbCBMeW5jaCBDcmVkaXQgUHJvZHVjdHMgTExDLUFsZ28=</CounterpartyEncodedLongName>
    <BloombergGlobalIdentifier>BBG005P7Q881</BloombergGlobalIdentifier>
    <SecondHalfTdr2TdrTrade>N</SecondHalfTdr2TdrTrade>
    <CashReversalOffsetTicketIndicator>N</CashReversalOffsetTicketIndicator>
  </Common>
  <TransactionCosts>
    <TransactionCost>
      <Type>2</Type>
      <Currency>USD</Currency>
      <Code>900013</Code>
      <Cost>298.19</Cost>
      <EffectOnFinalMoney>-</EffectOnFinalMoney>
      <Rate>0.0238</Rate>
      <CurrencyRate>1.0</CurrencyRate>
    </TransactionCost>
    <TransactionCost>
      <Type>3</Type>
      <Currency>USD</Currency>
      <Code>SEC</Code>
      <Cost>15.43</Cost>
      <EffectOnFinalMoney>-</EffectOnFinalMoney>
      <CurrencyRate>1.0</CurrencyRate>
    </TransactionCost>
  </TransactionCosts>
  <ExtendedFields>
    <Field calcrt="DS674" name="SECURITY_TYP2">Common Stock</Field>
    <Field calcrt="TX656" name="FEE_BASED_REPORTING" />
    <Field calcrt="TX657" name="ALTERNATIVE_TRADING_SYSTEM_MPID" />
    <Field calcrt="DX191" name="IS_PERPETUAL" />
    <Field calcrt="DS213" name="SECURITY_TYP">Common Stock</Field>
    <Field calcrt="TX658" name="TRANS_SETTLE_DATE_PLUS_1">20170719</Field>
    <Field calcrt="TX661" name="REPORTING_LKD_ID_COMPLEX_TRADE" />
    <Field calcrt="TX673" name="REPORTING_LINKAGE_TRADE_MARKER" />
    <Field calcrt="TX663" name="EXECUTION_VENUE_MIC_CODE" />
    <Field calcrt="TX664" name="REPORTING_LEI_CODE" />
    <Field calcrt="TX667" name="TRADE_UNIQUE_LIFECYCLE_ID" />
    <Field calcrt="TX669" name="MIFID_II_RPT_PST_TRADE_INDICATOR" />
    <Field calcrt="TX666" name="EXECUTION_VENUE_TRANSACTION_ID" />
    <Field calcrt="TX674" name="MIFID_II_TRADING_CAPACITY" />
    <Field calcrt="TX671" name="MIFID_II_DECISION_MAKER_WIN_FIRM" />
    <Field calcrt="TX672" name="MIFID_II_EXECUTION_WITHIN_FIRM" />
    <Field calcrt="TX668" name="MIFID_II_RPT_WAIVER_IND_TRA" />
    <Field calcrt="TX670" name="MIFID_II_RPT_SHORT_SELLING_IND" />
    <Field calcrt="TX665" name="TRADE_AS_OF_DATETIME_IN_UTC" />
  </ExtendedFields>
</TradeFeed>