using System;
using System.ServiceModel;
using Siepe.Shared.Service.Common.ServiceModelEx.Errors;
using Siepe.Shared.Service.Common.Contract.Fault;
using Siepe.Shared.Service.PublishSubscribe.Contract;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Service.BloombergRawMessage.Impl;
using System.Configuration;
using Siepe.Shared.Service.PublishSubscribe.Contract.Dto;
using System.Text;
using System.Diagnostics;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors;

namespace Siepe.Service.BloombergRawMessage.Web
{
    [ErrorHandlerBehavior]
    [ServiceBehavior(InstanceContextMode = InstanceContextMode.PerCall)]
    public class ServiceRawMessage : IServicePublication, IServiceRawMessage
    {
        private readonly IRawMessageHandler _handler;
        private readonly IFeeHandler feeHandler;

        public ServiceRawMessage(IRawMessageHandler handler, IFeeHandler feeHandler)
        {
            _handler = handler;
            this.feeHandler = feeHandler;
        }

        public void Publish(Message message)
        {
            if (message.BaseSubject == "Calc.Execution.Fees")
            {
                var executionId = Convert.ToInt64(Encoding.UTF8.GetString(message.Payload));
                feeHandler.CalcFees(executionId);

            }
            else if (message.Payload != null)
            {
                Trace.TraceInformation($"[BBG RAW] Bloomberg Raw Message Handler received message {message.BaseSubject}");
                string xml = Encoding.UTF8.GetString(message.Payload);
                _handler.Process(xml);
            }
        }

        #region IServiceCommon Members

        public void ClearCache()
        {

        }

        public string GetDomainName()
        {
            return ConfigurationManager.AppSettings["Domain"];
        }

        public string GetServiceName()
        {
            return _serviceName;
        }

        public string GetVersion()
        {
            return ConfigurationManager.AppSettings["Version"];
        }

        public string Ping()
        {
            return string.Empty;
        }

        public string GetReleaseNotes()
        {
            return string.Empty;
        }

        #endregion

        public string CallingUser { get; set; }

        private const string _serviceName = "BloombergRawMessage";
    }
}
