<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="Siepe.Shared.Service.PublishSubscribe.Contract" elementFormDefault="qualified" targetNamespace="Siepe.Shared.Service.PublishSubscribe.Contract" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost/BloombergRawData/ServiceRawMessage.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/Siepe.Shared.Service.PublishSubscribe.Contract.Dto" />
  <xs:element name="Publish">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/Siepe.Shared.Service.PublishSubscribe.Contract.Dto" minOccurs="0" name="newMessage" nillable="true" type="q1:Message" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>