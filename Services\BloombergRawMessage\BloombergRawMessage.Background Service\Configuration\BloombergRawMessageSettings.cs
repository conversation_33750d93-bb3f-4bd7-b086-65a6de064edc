namespace Siepe.Service.BloombergRawMessage.BackgroundWorker.Configuration
{
    /// <summary>
    /// Configuration settings for Bloomberg Raw Message service
    /// </summary>
    public class BloombergRawMessageSettings
    {
        /// <summary>
        /// Company name for trade processing
        /// </summary>
        public string Company { get; set; } = "HoldCo";

        /// <summary>
        /// Whether to process security master data
        /// </summary>
        public bool ProcessSecMaster { get; set; } = true;

        /// <summary>
        /// Whether fee processing is enabled
        /// </summary>
        public bool EnableFeeProcessing { get; set; } = false;

        /// <summary>
        /// Transaction configuration name used to load configuration from database
        /// </summary>
        public string TransactionConfig { get; set; } = "TradeFeedConfiguration";

        /// <summary>
        /// Domain/environment name
        /// </summary>
        public string Domain { get; set; } = "Development";

        /// <summary>
        /// Service version
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// Default issuer name for instruments
        /// </summary>
        public string DefaultIssuer { get; set; } = "Unknown Issuer";

        /// <summary>
        /// Location/office identifier
        /// </summary>
        public string Location { get; set; } = "Dallas";
    }
}
