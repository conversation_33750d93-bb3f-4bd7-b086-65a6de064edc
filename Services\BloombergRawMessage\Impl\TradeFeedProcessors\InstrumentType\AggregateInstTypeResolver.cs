﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class AggregateInstTypeResolver : IInstTypeResolver
    {
        public Dictionary<string, IInstrumentTypeHandler> _handlers { get; set; }

        public AggregateInstTypeResolver(IInstrumentTypeHandler[] handlers)
        {
            _handlers = handlers.ToDictionary(h => h.GetType().Name);
        }

        public IInstrumentTypeHandler Resolve(string instType)
        {
            string handlerName = instType + "Handler";
            if (!string.IsNullOrWhiteSpace(instType) && _handlers.Any(h => h.Key == handlerName))
            {
                return _handlers.First(handler => handler.Key == handlerName).Value;
            }
            return null;
        }
    }
}
