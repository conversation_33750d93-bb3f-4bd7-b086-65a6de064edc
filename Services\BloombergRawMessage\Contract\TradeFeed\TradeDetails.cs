﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Siepe.Service.BloombergRawMessage.Contract
{
    [XmlType("Common")]
    public class TradeDetails
    {
        [XmlElement]
        public string BloombergFirmID { get; set; }

        [XmlElement]
        public string TransactionNumber { get; set; }

        [XmlElement]
        public string SecurityIdentifierFlag { get; set; }

        [XmlElement]
        public string SecurityIdentifier { get; set; }

        [XmlElement]
        public string SecurityCurrencyISOCode { get; set; }

        [XmlElement]
        public string SecurityProductKey { get; set; }

        [XmlElement]
        public string BloombergIdentifier { get; set; }

        [XmlElement]
        public string Ticker { get; set; }

        [XmlElement]
        public string CouponStrikePrice { get; set; }

        [XmlElement]
        public string MaturityDateExpirationDate { get; set; }

        [XmlElement]
        public string SeriesExchangeCode { get; set; }

        [XmlElement]
        public string BuySellCoverShortFlag { get; set; }

        [XmlElement]
        public int RecordType { get; set; }

        [XmlElement]
        public string TradeDate { get; set; }

        [XmlElement]
        public string AsOfTradeDate { get; set; }

        [XmlElement]
        public string SettlementDate { get; set; }

        [XmlElement]
        public string Price { get; set; }

        [XmlElement]
        public string Yield { get; set; }

        [XmlElement]
        public string TradeAmount { get; set; }

        [XmlElement]
        public string ContractSize { get; set; }

        [XmlElement]
        public string MasterTicketNumber { get; set; }

        [XmlElement]
        public string CustomerAccountCounterparty { get; set; }

        [XmlElement]
        public string AccountCounterpartyShortName { get; set; }

        [XmlElement]
        public string SettlementLocationIndicator { get; set; }

        [XmlElement]
        public string ProductSubFlag { get; set; }

        [XmlElement]
        public string PrincipalLoanAmount { get; set; }

        [XmlArray("ShortNotes")]
        [XmlArrayItem("ShortNote")]
        public List<Note> ShortNotes { get; set; }

        [XmlArray("LongNotes")]
        [XmlArrayItem("LongNote")]
        public List<Note> LongNotes { get; set; }

        [XmlArray("StrategyTags")]
        [XmlArrayItem("StrategyTag")]
        public List<StrategyTag> StrategyTags { get; set; }

        [XmlElement]
        public string TraderAccountName { get; set; }

        [XmlElement]
        public string TimeOfSlate { get; set; }

        [XmlElement]
        public string TimeOfSalesTicket { get; set; }

        [XmlElement]
        public string LastLogin { get; set; }

        [XmlElement]
        public string SalespersonLogin { get; set; }

        [XmlElement]
        public string TransactionNumberOfOriginTrans { get; set; }

        [XmlElement]
        public string SettlementCurrencyISOCode { get; set; }

        [XmlElement]
        public string SettlementCurrencyRate { get; set; }

        [XmlElement]
        public string CancelDueToCorrection { get; set; }

        [XmlElement]
        public string InvertFlag { get; set; }

        [XmlElement]
        public string WorkoutDate { get; set; }

        [XmlElement]
        public string WorkoutPrice { get; set; }

        [XmlElement]
        public string AutoExTradeFlag { get; set; }

        [XmlElement]
        public string NameOfUserWhoAuthorizedTrade { get; set; }

        [XmlElement]
        public string DateTradeWasAuthorized { get; set; }

        [XmlElement]
        public string TraderToTraderTradeFlag { get; set; }

        [XmlElement]
        public string Version { get; set; }

        [XmlElement]
        public string UniqueBloombergID { get; set; }

        [XmlElement]
        public string ExtendedPrecisionPrice { get; set; }

        [XmlElement]
        public string SecondaryTransactionTicketNumber { get; set; }

        [XmlElement]
        public string SystemDate { get; set; }

        [XmlElement]
        public string ImpactFlag { get; set; }

        [XmlElement]
        public string SettlementLocationAbbreviation { get; set; }

        [XmlElement]
        public string TransactionType { get; set; }

        [XmlElement]
        public string EnteredTicketUserId { get; set; }

        [XmlElement]
        public string AllocatedTicketUserId { get; set; }

        [XmlArray]
        [XmlArrayItem("FunctionName")]
        public List<string> BloombergFunctions { get; set; }

        [XmlElement]
        public string OMSOrderNumber { get; set; }

        [XmlElement]
        public string QuoteTypeIndicator { get; set; }

        [XmlElement]
        public string IssueDate { get; set; }

        [XmlElement]
        public string PrimeBroker { get; set; }

        [XmlElement]
        public string SoftDollarFlag { get; set; }

        [XmlElement]
        public string OMSOrderType { get; set; }

        [XmlElement]
        public string OMSTimeInForce { get; set; }

        [XmlElement]
        public string TIPFactorEstimated { get; set; }

        [XmlElement]
        public string TotalTradeAmount { get; set; }

        [XmlElement]
        public string MasterAccount { get; set; }

        [XmlElement]
        public string MasterAccountName { get; set; }

        [XmlElement]
        public string CTMMatchStatus { get; set; }

        [XmlElement]
        public string MatchDate { get; set; }

        [XmlElement]
        public string IsDirtyPrice { get; set; }

        [XmlElement]
        public string TSAMIndicator { get; set; }

        [XmlElement]
        public string TaxLotMethod { get; set; }

        [XmlElement]
        public string SecurityPrice { get; set; }

        [XmlElement]
        public string SettlementAmount { get; set; }

        [XmlElement]
        public string BloombergReferenceNumber { get; set; }

        [XmlElement]
        public string FundCcyPrincipal { get; set; }

        [XmlElement]
        public string FundCcyTotalCommission { get; set; }

        [XmlElement]
        public string RedemptionCcyPrincipal { get; set; }

        [XmlElement]
        public string RedemptionTotalCommission { get; set; }

        [XmlElement]
        public string SettlementCcyPrincipal { get; set; }

        [XmlElement]
        public string SettlementCcyTotalCommission { get; set; }

        [XmlElement]
        public string ExecutionPlatform { get; set; }

        [XmlElement]
        public string ClientAuth { get; set; }

        [XmlElement]
        public string BasketID { get; set; }

        [XmlElement]
        public string OMSTransactionID { get; set; }

        [XmlElement]
        public string LastLoginUUID { get; set; }

        [XmlElement]
        public string OriginalTktId { get; set; }

        [XmlElement]
        public string SettlementCcyPrice { get; set; }

        [XmlElement]
        public string RTTMIndicator { get; set; }

        [XmlElement]
        public string RTTMReferenceID { get; set; }

        [XmlElement]
        public string CounterpartyEncodedLongName { get; set; }

        [XmlElement]
        public string GSpread { get; set; }

        [XmlElement]
        public string Convexity { get; set; }

        [XmlElement]
        public string GSpreadCurveID { get; set; }

        [XmlElement]
        public string AvgLife { get; set; }

        [XmlElement]
        public string BloombergGlobalIdentifier { get; set; }

        [XmlElement]
        public string SecondHalfTdr2TdrTrade { get; set; }

        [XmlElement]
        public string CashReversalOffsetTicketIndicator { get; set; }

        [XmlElement]
        public string AccruedInterestRepoInterest { get; set; }

        [XmlElement(ElementName = "isCFD", IsNullable = true)]
        public bool? IsCfd { get; set; }

        [XmlElement]
        public string MortgageBradyIndexBondFactor { get; set; }

        [XmlElement]
        public string TradeFlatFlag { get; set; }
    }
}
