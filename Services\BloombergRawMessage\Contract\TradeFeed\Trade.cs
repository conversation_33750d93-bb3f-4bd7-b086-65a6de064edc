﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Serialization;

namespace Siepe.Service.BloombergRawMessage.Contract
{
    [XmlRoot(ElementName = "TradeFeed", Namespace = "urn:tradefeed-xsd")]
    public class Trade : IRawObject
    {
        [XmlElement("Common")]
        public TradeDetails TradeDetails { get; set; }

        [XmlArray("TransactionCosts")]
        [XmlArrayItem("TransactionCost")]
        public List<TransactionCost> TransactionCosts { get; set; }

        [XmlArray("ExtendedFields")]
        [XmlArrayItem("Field")]
        public List<Field> ExtendedFields { get; set; }

        [XmlIgnore]
        public string InvestmentType { get; set; }
    }
}
