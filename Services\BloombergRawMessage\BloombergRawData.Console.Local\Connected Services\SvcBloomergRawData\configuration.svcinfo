﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.NetTcpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;NetTcpBinding_IServicePublication&quot; /&gt;" bindingType="netTcpBinding" name="NetTcpBinding_IServicePublication" />
    <binding digest="System.ServiceModel.Configuration.WSHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;WSHttpBinding_IServicePublication&quot;&gt;&lt;reliableSession enabled=&quot;true&quot; /&gt;&lt;/Data&gt;" bindingType="wsHttpBinding" name="WSHttpBinding_IServicePublication" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost/BloombergRawData/ServiceRawMessage.svc&quot; binding=&quot;wsHttpBinding&quot; bindingConfiguration=&quot;WSHttpBinding_IServicePublication&quot; contract=&quot;SvcBloomergRawData.IServicePublication&quot; name=&quot;WSHttpBinding_IServicePublication&quot;&gt;&lt;identity&gt;&lt;userPrincipalName value=&quot;<EMAIL>&quot; /&gt;&lt;/identity&gt;&lt;/Data&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost/BloombergRawData/ServiceRawMessage.svc&quot; binding=&quot;wsHttpBinding&quot; bindingConfiguration=&quot;WSHttpBinding_IServicePublication&quot; contract=&quot;SvcBloomergRawData.IServicePublication&quot; name=&quot;WSHttpBinding_IServicePublication&quot;&gt;&lt;identity&gt;&lt;userPrincipalName value=&quot;<EMAIL>&quot; /&gt;&lt;/identity&gt;&lt;/Data&gt;" contractName="SvcBloomergRawData.IServicePublication" name="WSHttpBinding_IServicePublication" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;net.tcp://siepe033.siepe.local/BloombergRawData/ServiceRawMessage.svc&quot; binding=&quot;netTcpBinding&quot; bindingConfiguration=&quot;NetTcpBinding_IServicePublication&quot; contract=&quot;SvcBloomergRawData.IServicePublication&quot; name=&quot;NetTcpBinding_IServicePublication&quot;&gt;&lt;identity&gt;&lt;userPrincipalName value=&quot;<EMAIL>&quot; /&gt;&lt;/identity&gt;&lt;/Data&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;net.tcp://siepe033.siepe.local/BloombergRawData/ServiceRawMessage.svc&quot; binding=&quot;netTcpBinding&quot; bindingConfiguration=&quot;NetTcpBinding_IServicePublication&quot; contract=&quot;SvcBloomergRawData.IServicePublication&quot; name=&quot;NetTcpBinding_IServicePublication&quot;&gt;&lt;identity&gt;&lt;userPrincipalName value=&quot;<EMAIL>&quot; /&gt;&lt;/identity&gt;&lt;/Data&gt;" contractName="SvcBloomergRawData.IServicePublication" name="NetTcpBinding_IServicePublication" />
  </endpoints>
</configurationSnapshot>