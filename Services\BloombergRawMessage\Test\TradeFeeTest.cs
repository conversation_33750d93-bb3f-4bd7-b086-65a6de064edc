﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.IO;
using Siepe.Service.BloombergRawMessage.Impl;
using System.Text;
using Rhino.Mocks;
using Siepe.Service.BloombergRawMessage.Contract;

namespace Test
{
    [TestClass]
    public class Tests
    {
        public string _xml { get; set; }

        private string _xmlFile = @"../../TradeFeeTest/testXml/Test.xml";

        [TestInitialize]
        public void Setup()
        {
            using (var reader = new StreamReader(_xmlFile))
            {
                _xml = reader.ReadToEnd();
            }
        }

        //[TestMethod]
        public void Debug()
        {
            
        }
    }
}
