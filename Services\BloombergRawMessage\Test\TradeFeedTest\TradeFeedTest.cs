﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Rhino.Mocks;
using Siepe.Expenses.Biz;
using Siepe.Expenses.Engine;
using Siepe.GenevaUtility;
using Siepe.GenevaUtility.Entities;
using Siepe.Orders.Entities;
using Siepe.PubSubUtility;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;
using Siepe.Shared.DBUtility;
using System.Collections.Generic;
using System.IO;
using AllocationPortfolio = Siepe.Allocations.Entities.Portfolio;
using OrderInstrument = Siepe.Orders.Entities.Instrument;

namespace Test.TradeFeedTest
{
    [TestClass]
    public class TradeFeedTest
    {
        private ITradeFeedConfigurationProvider mockTradeFeedConfigurationProvider;
        private ITransactionResolver mockTransactionResolver;
        private ITransactionType mockTransactionType;
        private IGenevaDataProvider mockGenevaDataProvider;
        private IPublicationService mockPubSubService;
        private IAllocationProvider mockAllocationProvider;
        private ICalculationEngine mockCalcEngine;
        private IFeeDataProvider mockFeeDataProvider;
        private IRawTradeProcessorProvider mockProcessorProvider;

        [TestInitialize]
        public void Initialize()
        {
            mockTradeFeedConfigurationProvider = MockRepository.GenerateMock<ITradeFeedConfigurationProvider>();
            mockTradeFeedConfigurationProvider.Stub(x => x.GetConfiguration()).Return(GetTestConfiguration());
            mockTransactionResolver = MockRepository.GenerateMock<ITransactionResolver>();
            mockTransactionType = MockRepository.GenerateMock<ITransactionType>();
            mockTransactionResolver.Stub(x => x.Resolve(Arg<string>.Is.Anything)).Return(mockTransactionType);

            mockGenevaDataProvider = MockRepository.GenerateMock<IGenevaDataProvider>();
            mockGenevaDataProvider.Stub(x => x.Save(Arg<RawTrade>.Is.Anything)).Return(1);

            mockPubSubService = MockRepository.GenerateMock<IPublicationService>();
            mockPubSubService.Stub(x => x.PublishMessage(0, null, null, null, null, null)).IgnoreArguments();

            mockAllocationProvider = MockRepository.GenerateMock<IAllocationProvider>();
            mockAllocationProvider.Stub(x => x.BuildAllocation(Arg<RawTrade>.Is.Anything)).Return(new Siepe.Allocations.Entities.Allocation
            {
                Execution = new Execution
                {
                    LastQty = 500000,
                    LastPrice = 101.25m,
                    OrderSide = new OrderParameter() { Name = "B" },
                    Broker = new Broker() { Code = "MSCO" },
                    Instrument = new OrderInstrument() { Id = 1, Name = "Inst", Type = "Equity" }
                },
                PercentOfOrder = 1,
                AllocationQty = 500000,
                Portfolio = new AllocationPortfolio
                {
                    ID = 1,
                    Name = "Portfolio",
                    Custodian = "Custodian"
                }
            });

            mockCalcEngine = MockRepository.GenerateMock<ICalculationEngine>();
            mockCalcEngine.Stub(x => x.CalculateExpenses(Arg<Siepe.Allocations.Entities.Allocation>.Is.Anything)).Return(new List<Siepe.Expenses.Entities.AppliedExpense>());
            mockCalcEngine.Stub(x => x.CalculateExpenses(Arg<Execution>.Is.Anything)).Return(new List<Siepe.Expenses.Entities.AppliedExpense> { });

            mockFeeDataProvider = MockRepository.GenerateMock<IFeeDataProvider>();
            mockFeeDataProvider.Stub(x => x.SaveExpenses(Arg<long>.Is.Anything, Arg<List<Siepe.Expenses.Entities.AppliedExpense>>.Is.Anything));

            mockProcessorProvider = MockRepository.GenerateMock<IRawTradeProcessorProvider>();
            mockProcessorProvider.Stub(x => x.GetRawTradeProcessors()).Return(new List<IRawTradeProcessor>());
        }

        [TestMethod]
        [TestCategory("Integration")]
        public void TestMethod1()
        {
            var tradefeedhandler = new TradeFeedHandler();
            tradefeedhandler.configurationProvider = mockTradeFeedConfigurationProvider;
            tradefeedhandler._transactionResolver = mockTransactionResolver;
            tradefeedhandler.publicationService = mockPubSubService;
            tradefeedhandler.allocationProvider = mockAllocationProvider;
            tradefeedhandler._calcEngine = mockCalcEngine;
            tradefeedhandler._feeProvider = mockFeeDataProvider;
            tradefeedhandler.rawTradeProcessorProvider = mockProcessorProvider;

            var trade = GetTestTrade("Test.xml");

            tradefeedhandler.Process(trade);
        }

        private TradeFeedConfiguration GetTestConfiguration()
        {
            return new TradeFeedConfiguration
            {
                FieldMappings = new List<TradeFeedConfigurationItem>
                {
                    new TradeFeedConfigurationItem
                    {
                        Key = "NetInvestmentAmount",
                        Value = "CALC"
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "InvestmentPrefix",
                        Value = "AltKey1="
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "TicketPrefix",
                        Value = "BB"
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "NetCounterAmount",
                        Value = "CALC"
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "CounterFXDenomination",
                        Value = "USD"
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "NetTradeFlag",
                        Value = "0"
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "AccrueCommissionFlag",
                        Value = "0"
                    },
                    new TradeFeedConfigurationItem
                    {
                        Key = "TradeFX",
                        Value = "1"
                    }
                },
                TransactionTypes = new List<TransactionTypeConfiguration>
                {
                    new TransactionTypeConfiguration
                    {
                        Raw = "B",
                        Flag = "B",
                        TransactionType = "BuyNewTransaction",
                        CreateMessageType = "Buy_New",
                        CancelMessageType = "Buy_Delete"
                    },
                    new TransactionTypeConfiguration
                    {
                        Raw = "S",
                        Flag = "S",
                        TransactionType = "SellNewTransaction",
                        CreateMessageType = "Sell_New",
                        CancelMessageType = "Sell_Delete"
                    },
                    new TransactionTypeConfiguration
                    {
                        Raw = "H",
                        Flag = "SS",
                        TransactionType = "SellShortNewTransaction",
                        CreateMessageType = "SellShort_New",
                        CancelMessageType = "SellShort_Delete"
                    },
                    new TransactionTypeConfiguration
                    {
                        Raw = "C",
                        Flag = "BC",
                        TransactionType = "CoverShortNewTransaction",
                        CreateMessageType = "CoverShort_New",
                        CancelMessageType = "CoverShort_Delete"
                    }
                },
                CreateRecordTypes = new List<int> { 2, 6, 202 },
                CancelRecordTypes = new List<int> { 102, 302 },
                Location = "Advent Geneva"
            };
        }

        private Trade GetTestTrade(string filename)
        {
            var xml = File.ReadAllText($"TradeFeedTest\\testXml\\{filename}");
            var xmlHelper = new XmlHelper<Trade>();

            return xmlHelper.Deserialize(xml);
        }
    }
}
