﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.GenevaUtility.Entities;
using Siepe.Shared.DBUtility;
using System.Data.SqlClient;

namespace Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor
{
    public class CfdSwapProcessor : IRawTradeProcessor
    {
        private ISqlDbAccess sqlDbAccess;

        private object objLock = new object();

        public CfdSwapProcessor(ISqlDbAccess sqlDbAccess)
        {
            this.sqlDbAccess = sqlDbAccess;
        }

        public void Process(RawTrade trade)
        {
            if(trade.CfdSwapFlag ?? false)
            {
                var bloombergUniqueId = trade.BloombergID;
                var portfolio = trade.Portfolio;
                var locationAccount = trade.LocationAccount;
                if(string.IsNullOrEmpty(bloombergUniqueId) || string.IsNullOrEmpty(portfolio))
                {
                    return;
                }

                lock (objLock)
                {
                    if (!SwapExist(bloombergUniqueId, portfolio, locationAccount))
                    {
                        CreateNewSwap(bloombergUniqueId, portfolio, locationAccount);
                    }
                }
            }
        }

        private bool SwapExist(string bloombergId, string portfolio, string locationAccount)
        {
            return (bool)sqlDbAccess.ExecuteScalar("dbo.pSwapExists", 
                new[] { new SqlParameter("@BloombergUniqueID", bloombergId), new SqlParameter("@Portfolio", portfolio), new SqlParameter("@LocationAccount", locationAccount)});
        }

        private void CreateNewSwap(string bloombergId, string portfolio, string locationAccount)
        {
            sqlDbAccess.ExecuteNonQuery("dbo.pSwapCreate",
                new[] { new SqlParameter("@BloombergUniqueID", bloombergId), new SqlParameter("@Portfolio", portfolio), new SqlParameter("@LocationAccount", locationAccount)});
        }
    }
}
