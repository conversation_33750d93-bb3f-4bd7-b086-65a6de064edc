﻿using Siepe.GenevaUtility.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class SellShortNewTransaction : BaseTransaction<SellShortNewType>, ITransactionType
    {
        public void ProcessTrade(Trade trade, TradeFeedConfiguration config)
        {
            GenevaLoader loader = new GenevaLoader();
            loader.TransactionRecords = new GenevaLoaderTransactionRecords();
            SellShortNewType transaction = BuildTransactionRecord(trade, config);
            loader.TransactionRecords.SellShort_New = new[] { transaction };
            SendCreateMessage(loader);
        }

        public void CancelTrade(string transactionId, Trade trade, TradeFeedConfiguration config)
        {
            SellShortDeleteType deleteType = new SellShortDeleteType()
            {
                KeyValue = new TransactionRecordsKeyValueType() { Value = transactionId }
            };
            GenevaLoader loader = new GenevaLoader()
            {
                TransactionRecords = new GenevaLoaderTransactionRecords()
                {
                    SellShort_Delete = new[] { deleteType }
                }
            };
            _dataProvider.Cancel(transactionId);
        }
    }
}
