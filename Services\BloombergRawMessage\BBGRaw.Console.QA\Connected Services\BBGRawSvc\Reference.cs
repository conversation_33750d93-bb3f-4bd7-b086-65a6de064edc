﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace BBGRaw.Console.QA.BBGRawSvc {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Message", Namespace="http://schemas.datacontract.org/2004/07/Siepe.Shared.Service.PublishSubscribe.Con" +
        "tract.Dto")]
    [System.SerializableAttribute()]
    public partial class Message : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string BaseSubjectField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime CreatedDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CreatedUserField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string DescriptionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private long IDField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private byte[] PayloadField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SubjectField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SubscriptionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string TitleField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BaseSubject {
            get {
                return this.BaseSubjectField;
            }
            set {
                if ((object.ReferenceEquals(this.BaseSubjectField, value) != true)) {
                    this.BaseSubjectField = value;
                    this.RaisePropertyChanged("BaseSubject");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreatedDate {
            get {
                return this.CreatedDateField;
            }
            set {
                if ((this.CreatedDateField.Equals(value) != true)) {
                    this.CreatedDateField = value;
                    this.RaisePropertyChanged("CreatedDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedUser {
            get {
                return this.CreatedUserField;
            }
            set {
                if ((object.ReferenceEquals(this.CreatedUserField, value) != true)) {
                    this.CreatedUserField = value;
                    this.RaisePropertyChanged("CreatedUser");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description {
            get {
                return this.DescriptionField;
            }
            set {
                if ((object.ReferenceEquals(this.DescriptionField, value) != true)) {
                    this.DescriptionField = value;
                    this.RaisePropertyChanged("Description");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public long ID {
            get {
                return this.IDField;
            }
            set {
                if ((this.IDField.Equals(value) != true)) {
                    this.IDField = value;
                    this.RaisePropertyChanged("ID");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public byte[] Payload {
            get {
                return this.PayloadField;
            }
            set {
                if ((object.ReferenceEquals(this.PayloadField, value) != true)) {
                    this.PayloadField = value;
                    this.RaisePropertyChanged("Payload");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Subject {
            get {
                return this.SubjectField;
            }
            set {
                if ((object.ReferenceEquals(this.SubjectField, value) != true)) {
                    this.SubjectField = value;
                    this.RaisePropertyChanged("Subject");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Subscription {
            get {
                return this.SubscriptionField;
            }
            set {
                if ((object.ReferenceEquals(this.SubscriptionField, value) != true)) {
                    this.SubscriptionField = value;
                    this.RaisePropertyChanged("Subscription");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Title {
            get {
                return this.TitleField;
            }
            set {
                if ((object.ReferenceEquals(this.TitleField, value) != true)) {
                    this.TitleField = value;
                    this.RaisePropertyChanged("Title");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="Siepe.Shared.Service.PublishSubscribe.Contract", ConfigurationName="BBGRawSvc.IServicePublication")]
    public interface IServicePublication {
        
        [System.ServiceModel.OperationContractAttribute(IsOneWay=true, Action="Siepe.Shared.Service.PublishSubscribe.Contract/IServicePublication/Publish")]
        void Publish(BBGRaw.Console.QA.BBGRawSvc.Message newMessage);
        
        [System.ServiceModel.OperationContractAttribute(IsOneWay=true, Action="Siepe.Shared.Service.PublishSubscribe.Contract/IServicePublication/Publish")]
        System.Threading.Tasks.Task PublishAsync(BBGRaw.Console.QA.BBGRawSvc.Message newMessage);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IServicePublicationChannel : BBGRaw.Console.QA.BBGRawSvc.IServicePublication, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class ServicePublicationClient : System.ServiceModel.ClientBase<BBGRaw.Console.QA.BBGRawSvc.IServicePublication>, BBGRaw.Console.QA.BBGRawSvc.IServicePublication {
        
        public ServicePublicationClient() {
        }
        
        public ServicePublicationClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public ServicePublicationClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServicePublicationClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public ServicePublicationClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public void Publish(BBGRaw.Console.QA.BBGRawSvc.Message newMessage) {
            base.Channel.Publish(newMessage);
        }
        
        public System.Threading.Tasks.Task PublishAsync(BBGRaw.Console.QA.BBGRawSvc.Message newMessage) {
            return base.Channel.PublishAsync(newMessage);
        }
    }
}
