<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:i0="Siepe.Shared.Service.PublishSubscribe.Contract" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ServiceRawMessage" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="WSHttpBinding_IServicePublication_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <wsrm:RMAssertion xmlns:wsrm="http://schemas.xmlsoap.org/ws/2005/02/rm/policy">
          <wsrm:InactivityTimeout Milliseconds="600000" />
          <wsrm:AcknowledgementInterval Milliseconds="200" />
        </wsrm:RMAssertion>
        <sp:SymmetricBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:ProtectionToken>
              <wsp:Policy>
                <sp:SecureConversationToken sp:IncludeToken="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy/IncludeToken/AlwaysToRecipient">
                  <wsp:Policy>
                    <sp:RequireDerivedKeys />
                    <sp:BootstrapPolicy>
                      <wsp:Policy>
                        <sp:SignedParts>
                          <sp:Body />
                          <sp:Header Name="To" Namespace="http://www.w3.org/2005/08/addressing" />
                          <sp:Header Name="From" Namespace="http://www.w3.org/2005/08/addressing" />
                          <sp:Header Name="FaultTo" Namespace="http://www.w3.org/2005/08/addressing" />
                          <sp:Header Name="ReplyTo" Namespace="http://www.w3.org/2005/08/addressing" />
                          <sp:Header Name="MessageID" Namespace="http://www.w3.org/2005/08/addressing" />
                          <sp:Header Name="RelatesTo" Namespace="http://www.w3.org/2005/08/addressing" />
                          <sp:Header Name="Action" Namespace="http://www.w3.org/2005/08/addressing" />
                        </sp:SignedParts>
                        <sp:EncryptedParts>
                          <sp:Body />
                        </sp:EncryptedParts>
                        <sp:SymmetricBinding>
                          <wsp:Policy>
                            <sp:ProtectionToken>
                              <wsp:Policy>
                                <sp:SpnegoContextToken sp:IncludeToken="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy/IncludeToken/AlwaysToRecipient">
                                  <wsp:Policy>
                                    <sp:RequireDerivedKeys />
                                  </wsp:Policy>
                                </sp:SpnegoContextToken>
                              </wsp:Policy>
                            </sp:ProtectionToken>
                            <sp:AlgorithmSuite>
                              <wsp:Policy>
                                <sp:Basic256 />
                              </wsp:Policy>
                            </sp:AlgorithmSuite>
                            <sp:Layout>
                              <wsp:Policy>
                                <sp:Strict />
                              </wsp:Policy>
                            </sp:Layout>
                            <sp:IncludeTimestamp />
                            <sp:EncryptSignature />
                            <sp:OnlySignEntireHeadersAndBody />
                          </wsp:Policy>
                        </sp:SymmetricBinding>
                        <sp:Wss11>
                          <wsp:Policy />
                        </sp:Wss11>
                        <sp:Trust10>
                          <wsp:Policy>
                            <sp:MustSupportIssuedTokens />
                            <sp:RequireClientEntropy />
                            <sp:RequireServerEntropy />
                          </wsp:Policy>
                        </sp:Trust10>
                      </wsp:Policy>
                    </sp:BootstrapPolicy>
                  </wsp:Policy>
                </sp:SecureConversationToken>
              </wsp:Policy>
            </sp:ProtectionToken>
            <sp:AlgorithmSuite>
              <wsp:Policy>
                <sp:Basic256 />
              </wsp:Policy>
            </sp:AlgorithmSuite>
            <sp:Layout>
              <wsp:Policy>
                <sp:Strict />
              </wsp:Policy>
            </sp:Layout>
            <sp:IncludeTimestamp />
            <sp:EncryptSignature />
            <sp:OnlySignEntireHeadersAndBody />
          </wsp:Policy>
        </sp:SymmetricBinding>
        <sp:Wss11 xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy />
        </sp:Wss11>
        <sp:Trust10 xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:MustSupportIssuedTokens />
            <sp:RequireClientEntropy />
            <sp:RequireServerEntropy />
          </wsp:Policy>
        </sp:Trust10>
        <wsaw:UsingAddressing />
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsp:Policy wsu:Id="WSHttpBinding_IServicePublication_Publish_Input_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <sp:SignedParts xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <sp:Body />
          <sp:Header Name="CoordinationContext" Namespace="http://schemas.xmlsoap.org/ws/2004/10/wscoor" />
          <sp:Header Name="CoordinationContext" Namespace="http://docs.oasis-open.org/ws-tx/wscoor/2006/06" />
          <sp:Header Name="OleTxTransaction" Namespace="http://schemas.microsoft.com/ws/2006/02/tx/oletx" />
          <sp:Header Name="IssuedTokens" Namespace="http://schemas.xmlsoap.org/ws/2005/02/trust" />
          <sp:Header Name="Sequence" Namespace="http://schemas.xmlsoap.org/ws/2005/02/rm" />
          <sp:Header Name="SequenceAcknowledgement" Namespace="http://schemas.xmlsoap.org/ws/2005/02/rm" />
          <sp:Header Name="AckRequested" Namespace="http://schemas.xmlsoap.org/ws/2005/02/rm" />
          <sp:Header Name="To" Namespace="http://www.w3.org/2005/08/addressing" />
          <sp:Header Name="From" Namespace="http://www.w3.org/2005/08/addressing" />
          <sp:Header Name="FaultTo" Namespace="http://www.w3.org/2005/08/addressing" />
          <sp:Header Name="ReplyTo" Namespace="http://www.w3.org/2005/08/addressing" />
          <sp:Header Name="MessageID" Namespace="http://www.w3.org/2005/08/addressing" />
          <sp:Header Name="RelatesTo" Namespace="http://www.w3.org/2005/08/addressing" />
          <sp:Header Name="Action" Namespace="http://www.w3.org/2005/08/addressing" />
        </sp:SignedParts>
        <sp:EncryptedParts xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <sp:Body />
          <sp:Header Name="CoordinationContext" Namespace="http://schemas.xmlsoap.org/ws/2004/10/wscoor" />
          <sp:Header Name="CoordinationContext" Namespace="http://docs.oasis-open.org/ws-tx/wscoor/2006/06" />
          <sp:Header Name="OleTxTransaction" Namespace="http://schemas.microsoft.com/ws/2006/02/tx/oletx" />
          <sp:Header Name="IssuedTokens" Namespace="http://schemas.xmlsoap.org/ws/2005/02/trust" />
        </sp:EncryptedParts>
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsp:Policy wsu:Id="NetTcpBinding_IServicePublication_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <msb:BinaryEncoding xmlns:msb="http://schemas.microsoft.com/ws/06/2004/mspolicy/netbinary1" />
        <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:TransportToken>
              <wsp:Policy>
                <msf:WindowsTransportSecurity xmlns:msf="http://schemas.microsoft.com/ws/2006/05/framing/policy">
                  <msf:ProtectionLevel>EncryptAndSign</msf:ProtectionLevel>
                </msf:WindowsTransportSecurity>
              </wsp:Policy>
            </sp:TransportToken>
            <sp:AlgorithmSuite>
              <wsp:Policy>
                <sp:Basic256 />
              </wsp:Policy>
            </sp:AlgorithmSuite>
            <sp:Layout>
              <wsp:Policy>
                <sp:Strict />
              </wsp:Policy>
            </sp:Layout>
          </wsp:Policy>
        </sp:TransportBinding>
        <wsaw:UsingAddressing />
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:import namespace="Siepe.Shared.Service.PublishSubscribe.Contract" location="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?wsdl=wsdl0" />
  <wsdl:types />
  <wsdl:binding name="WSHttpBinding_IServicePublication" type="i0:IServicePublication">
    <wsp:PolicyReference URI="#WSHttpBinding_IServicePublication_policy" />
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Publish">
      <soap12:operation soapAction="Siepe.Shared.Service.PublishSubscribe.Contract/IServicePublication/Publish" style="document" />
      <wsdl:input>
        <wsp:PolicyReference URI="#WSHttpBinding_IServicePublication_Publish_Input_policy" />
        <soap12:body use="literal" />
      </wsdl:input>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="NetTcpBinding_IServicePublication" type="i0:IServicePublication">
    <wsp:PolicyReference URI="#NetTcpBinding_IServicePublication_policy" />
    <soap12:binding transport="http://schemas.microsoft.com/soap/tcp" />
    <wsdl:operation name="Publish">
      <soap12:operation soapAction="Siepe.Shared.Service.PublishSubscribe.Contract/IServicePublication/Publish" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ServiceRawMessage">
    <wsdl:port name="WSHttpBinding_IServicePublication" binding="tns:WSHttpBinding_IServicePublication">
      <soap12:address location="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc" />
      <wsa10:EndpointReference>
        <wsa10:Address>http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc</wsa10:Address>
        <Identity xmlns="http://schemas.xmlsoap.org/ws/2006/02/addressingidentity">
          <Upn><EMAIL></Upn>
        </Identity>
      </wsa10:EndpointReference>
    </wsdl:port>
    <wsdl:port name="NetTcpBinding_IServicePublication" binding="tns:NetTcpBinding_IServicePublication">
      <soap12:address location="net.tcp://005s03.highland.aws/BloombergRawData/ServiceRawMessage.svc" />
      <wsa10:EndpointReference>
        <wsa10:Address>net.tcp://005s03.highland.aws/BloombergRawData/ServiceRawMessage.svc</wsa10:Address>
        <Identity xmlns="http://schemas.xmlsoap.org/ws/2006/02/addressingidentity">
          <Upn><EMAIL></Upn>
        </Identity>
      </wsa10:EndpointReference>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>