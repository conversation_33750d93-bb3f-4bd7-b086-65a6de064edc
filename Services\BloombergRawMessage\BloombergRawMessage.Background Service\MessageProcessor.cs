using Siepe.Service.BloombergRawMessage.Impl;
using Siepe.Shared.Service.PublishSubscribe.Contract.Dto;
using Microsoft.Extensions.Logging;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.BackgroundWorker
{
    public class MessageProcessor
    {
        private readonly IRawMessageHandler _handler;
        private readonly ILogger<MessageProcessor> _logger;

        public MessageProcessor(IRawMessageHandler handler, ILogger<MessageProcessor> logger)
        {
            _handler = handler ?? throw new ArgumentNullException(nameof(handler));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Processes a message received from RabbitMQ or other transport.
        /// This mimics the limited scope Publish method from ServiceRawMessage.
        /// Excludes Calc.Execution.Fees handling as per requirements.
        /// </summary>
        /// <param name="message">The message to process</param>
        public void ProcessMessage(string message)
        {
            try
            {
                if (message == null)
                {
                    _logger.LogWarning("[BBG RAW Service] Received null message, skipping processing.");
                    return;
                }


                // Process only messages with payload (Bloomberg trade data)
                if (message!= null)
                {
                    _logger.LogInformation("[BBG RAW Service] Processing message with subject: {Subject}", message);

                    string xml = message;

                    if (string.IsNullOrWhiteSpace(xml))
                    {
                        _logger.LogWarning("[BBG RAW Service] Message payload is empty or whitespace, skipping processing.");
                        return;
                    }

                    // Process the XML through the existing handler
                    _handler.Process(xml);

                    _logger.LogInformation("[BBG RAW Service] Successfully processed message with subject: {Subject}", message);
                }
                else
                {
                    _logger.LogWarning("[BBG RAW Service] Message with subject '{Subject}' has no payload, skipping processing.", message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[BBG RAW Service] Error processing message");

                // Log message details for debugging (be careful with sensitive data)
                if (message != null)
                {
                    _logger.LogError("[BBG RAW Service] Failed message subject: {Subject}", message);
                    _logger.LogError("[BBG RAW Service] Failed message payload length: {PayloadLength}", message?.Length ?? 0);
                }

                // Re-throw to allow higher-level error handling
                throw;
            }
        }

        /// <summary>
        /// Processes a message asynchronously.
        /// </summary>
        /// <param name="message">The message to process</param>
        /// <returns>Task representing the async operation</returns>
        public async Task ProcessMessageAsync(string message)
        {
            await Task.Run(() => ProcessMessage(message));
        }
    }
}
