﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{63602458-AA82-4F65-B083-DC5D62F028E2}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Siepe.Service.BloombergRawMessage.Web</RootNamespace>
    <AssemblyName>Siepe.Service.BloombergRawMessage.Web</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <UseIISExpress>false</UseIISExpress>
    <IISExpressSSLPort />
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <TargetFrameworkProfile />
    <Use64BitIISExpress />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Development|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisLogFile>bin\Hcmlp.Service.GenevaAdapter.Web.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisLogFile>bin\Hcmlp.Service.GenevaAdapter.Web.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisLogFile>bin\Hcmlp.Service.GenevaAdapter.Web.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Caching, Version=5.0.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\packages\EnterpriseLibrary.Caching.5.0.505.0\lib\NET35\Microsoft.Practices.EnterpriseLibrary.Caching.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\packages\CommonServiceLocator.1.3\lib\portable-net4+sl5+netcore45+wpa81+wp8\Microsoft.Practices.ServiceLocation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\packages\Unity.3.5.1404.0\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\packages\Unity.3.5.1404.0\lib\net45\Microsoft.Practices.Unity.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\packages\Unity.Interception.3.5.1404.0\lib\Net45\Microsoft.Practices.Unity.Interception.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Interception.Configuration, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\packages\Unity.Interception.3.5.1404.0\lib\Net45\Microsoft.Practices.Unity.Interception.Configuration.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention">
      <HintPath>..\..\..\packages\Unity.3.5.1404.0\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.Common.Contract, Version=4.5.6498.13866, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Siepe.Shared.Service.Common.4.5.1\lib\Siepe.Shared.Service.Common.Contract.dll</HintPath>
    </Reference>
    <Reference Include="Siepe.Shared.Service.Common.Impl, Version=4.5.6498.13866, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Siepe.Shared.Service.Common.4.5.1\lib\Siepe.Shared.Service.Common.Impl.dll</HintPath>
    </Reference>
    <Reference Include="Siepe.Shared.Service.Common.ServiceModelEx, Version=4.5.6498.13866, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Siepe.Shared.Service.Common.4.5.1\lib\Siepe.Shared.Service.Common.ServiceModelEx.dll</HintPath>
    </Reference>
    <Reference Include="Siepe.Shared.Service.Configuration.Adapter">
      <HintPath>..\..\..\packages\Siepe.Shared.Service.Configuration.Assemblies.4.5.14353.1\lib\Siepe.Shared.Service.Configuration.Adapter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.Configuration.Contract">
      <HintPath>..\..\..\packages\Siepe.Shared.Service.Configuration.Assemblies.4.5.14353.1\lib\Siepe.Shared.Service.Configuration.Contract.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.Configuration.Impl">
      <HintPath>..\..\..\packages\Siepe.Shared.Service.Configuration.Assemblies.4.5.14353.1\lib\Siepe.Shared.Service.Configuration.Impl.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.PublishSubscribe.Adapter">
      <HintPath>..\..\..\packages\Siepe.Services.PublishSubscribe.Assemblies.4.5.20160825.1\lib\Siepe.Shared.Service.PublishSubscribe.Adapter.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.PublishSubscribe.Contract">
      <HintPath>..\..\..\packages\Siepe.Services.PublishSubscribe.Assemblies.4.5.20160825.1\lib\Siepe.Shared.Service.PublishSubscribe.Contract.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.PublishSubscribe.Impl">
      <HintPath>..\..\..\packages\Siepe.Services.PublishSubscribe.Assemblies.4.5.20160825.1\lib\Siepe.Shared.Service.PublishSubscribe.Impl.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Service.PublishSubscribe.ServiceModelEx">
      <HintPath>..\..\..\packages\Siepe.Services.PublishSubscribe.Assemblies.4.5.20160825.1\lib\Siepe.Shared.Service.PublishSubscribe.ServiceModelEx.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Siepe.Shared.Utils, Version=4.0.0.0, Culture=neutral, PublicKeyToken=dceafd3d7dd364cf, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Siepe.Shared.Utils.4.0.2\lib\Siepe.Shared.Utils.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Configuration.ConfigurationManager.7.0.0\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Data.SqlClient, Version=4.6.1.6, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\..\..\packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.Permissions.7.0.0\lib\net462\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel.Activation" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ServiceModel.Web" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Extensions.Design" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ServiceRawTrade.svc" />
    <Content Include="ServiceRawMessage.svc" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Bootstrapper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServiceRawTrade.svc.cs">
      <DependentUpon>ServiceRawTrade.svc</DependentUpon>
    </Compile>
    <Compile Include="ServiceRawMessage.svc.cs">
      <DependentUpon>ServiceRawMessage.svc</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.config">
      <SubType>Designer</SubType>
    </Content>
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="packages.config">
      <SubType>Designer</SubType>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.Highland-Production.config">
      <DependentUpon>Web.config</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Web.Highland-QA.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility\DBUtility.csproj">
      <Project>{9354c545-4a3a-48c1-91c5-387a1d10b6dc}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Biz\Expenses.Biz.csproj">
      <Project>{b36a1f71-063d-4fe1-b2ce-f4472d872eba}</Project>
      <Name>Expenses.Biz</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Engine\Expenses.Engine.csproj">
      <Project>{c739b218-01f5-494a-883a-a8234134e017}</Project>
      <Name>Expenses.Engine</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Entities\Expenses.Entities.csproj">
      <Project>{3668404e-399d-4203-b8d4-eab9f0cb3e60}</Project>
      <Name>Expenses.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility.Entities\GenevaUtility.Entities.csproj">
      <Project>{2d7e7414-899b-4448-9901-ad9635399280}</Project>
      <Name>GenevaUtility.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility\GenevaUtility.csproj">
      <Project>{825c9037-535e-4daf-9c0c-3422a43d973c}</Project>
      <Name>GenevaUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Data\Siepe.Instruments.Data.csproj">
      <Project>{0bb29d1e-443b-4b4f-9fdd-f44e08ae937a}</Project>
      <Name>Siepe.Instruments.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Entities\Siepe.Instruments.Entities.csproj">
      <Project>{5027171f-0eb6-4204-8d2d-eb2db6c8832d}</Project>
      <Name>Siepe.Instruments.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\IssuerCreation\IssuerCreation\IssuerCreation.csproj">
      <Project>{ac088000-023b-4809-b3fb-d2893ff81b99}</Project>
      <Name>IssuerCreation</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\PubSubUtility\PubSubUtility\PubSubUtility.csproj">
      <Project>{F139C84B-FBA0-4BE7-BF4E-21B24757FD87}</Project>
      <Name>PubSubUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.DataLayer\Siepe.RulesEngine.DataLayer.csproj">
      <Project>{26b98504-bf45-45f7-a92e-b202d42c7c29}</Project>
      <Name>Siepe.RulesEngine.DataLayer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.IronPython\Siepe.RulesEngine.IronPython.csproj">
      <Project>{eddce863-48f1-4248-88ba-7ef47d2f23d3}</Project>
      <Name>Siepe.RulesEngine.IronPython</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.Services\Siepe.RulesEngine.Services.csproj">
      <Project>{86db5604-0697-4eb7-aadc-6e070237be57}</Project>
      <Name>Siepe.RulesEngine.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Serialization\JsonSerializer\JsonSerializer.csproj">
      <Project>{8c7b95f4-9fee-4ee1-976e-ed1eacc59a3f}</Project>
      <Name>JsonSerializer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Serialization\Serialization\Serialization.csproj">
      <Project>{601390c0-ed1a-4d53-a2fe-1497ecaa8fdb}</Project>
      <Name>Serialization</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\UnityUtility\UnityUtility\UnityUtility.csproj">
      <Project>{1a6f5cc7-abf6-4a07-9c44-8aa701cc081d}</Project>
      <Name>UnityUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\WCFUnityUtility\WCFUnityUtility\WCFUnityUtility.csproj">
      <Project>{470740F7-760D-4CF2-9B06-53664092A1CC}</Project>
      <Name>WCFUnityUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Contract\Contract.csproj">
      <Project>{AE7F4C21-DA16-4598-9AAF-55BBF4F899FF}</Project>
      <Name>Contract</Name>
    </ProjectReference>
    <ProjectReference Include="..\Impl\Impl.csproj">
      <Project>{5B33058A-3455-4CFB-B72C-0BEA1B93CF5E}</Project>
      <Name>Impl</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.Highland-Development.config">
      <DependentUpon>Web.config</DependentUpon>
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="_Web.nuspec">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.Nexpoint-Production.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Web.Nexpoint-Development.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Web.Nexpoint-QA.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Highland-Development|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Highland-QA|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Highland-Production|AnyCPU'">
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <SaveServerSettingsInUserFile>True</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
</Project>