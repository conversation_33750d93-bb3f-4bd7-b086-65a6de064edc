﻿<!--This should not be passed-->
<TradeFeed xmlns="urn:tradefeed-xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Common>
    <BloombergFirmID>3461</BloombergFirmID>
    <TransactionNumber>2829576</TransactionNumber>
    <SecurityIdentifierFlag>1</SecurityIdentifierFlag>
    <SecurityIdentifier>BL096577</SecurityIdentifier>
    <SecurityCurrencyISOCode>USD</SecurityCurrencyISOCode>
    <SecurityProductKey>8</SecurityProductKey>
    <BloombergIdentifier>BL0965774   </BloombergIdentifier>
    <Ticker>WTW</Ticker>
    <MaturityDateExpirationDate>2020-04-02</MaturityDateExpirationDate>
    <SeriesExchangeCode>1</SeriesExchangeCode>
    <BuySellCoverShortFlag>S</BuySellCoverShortFlag>
    <RecordType>2</RecordType>
    <TradeDate>2017-08-10T19:06:00</TradeDate>
    <AsOfTradeDate>2017-08-10T17:20:37</AsOfTradeDate>
    <Price>99.0</Price>
    <TradeAmount>1362383.15</TradeAmount>
    <CustomerAccountCounterparty>DBAB</CustomerAccountCounterparty>
    <AccountCounterpartyShortName>DBAB</AccountCounterpartyShortName>
    <PrincipalAgencyFlag>P</PrincipalAgencyFlag>
    <SettlementLocationIndicator>2</SettlementLocationIndicator>
    <ProductSubFlag>99</ProductSubFlag>
    <PrincipalLoanAmount>1348759.32</PrincipalLoanAmount>
    <ShortNotes>
      <ShortNote>
        <Index>2</Index>
        <Text>STST        </Text>
      </ShortNote>
      <ShortNote>
        <Index>3</Index>
        <Text>N           </Text>
      </ShortNote>
    </ShortNotes>
    <LongNotes>
      <LongNote>
        <Index>3</Index>
        <Text>USD                                          </Text>
      </LongNote>
      <LongNote>
        <Index>4</Index>
        <Text>948627AU8US948627AU80                        </Text>
      </LongNote>
    </LongNotes>
    <TraderAccountName>PCSFMSTR</TraderAccountName>
    <TimeOfSlate>11:10:24</TimeOfSlate>
    <TimeOfSalesTicket>18:56:44</TimeOfSalesTicket>
    <LastLogin>NBURNS19</LastLogin>
    <SalespersonLogin>CHAYES75</SalespersonLogin>
    <MasterTicketNumber>2829574</MasterTicketNumber>
    <SettlementCurrencyISOCode>USD</SettlementCurrencyISOCode>
    <SettlementCurrencyRate>1.0</SettlementCurrencyRate>
    <CancelDueToCorrection>N</CancelDueToCorrection>
    <TradeFlatFlag>true</TradeFlatFlag>
    <InvertFlag>N</InvertFlag>
    <WorkoutDate>2017-08-21</WorkoutDate>
    <WorkoutPrice>100.0</WorkoutPrice>
    <AutoExTradeFlag>N</AutoExTradeFlag>
    <NameOfUserWhoAuthorizedTrade>AUTO-RELEASE</NameOfUserWhoAuthorizedTrade>
    <DateTradeWasAuthorized>2017-08-10</DateTradeWasAuthorized>
    <TraderToTraderTradeFlag>N</TraderToTraderTradeFlag>
    <Version>2000</Version>
    <UniqueBloombergID>COBL0965774</UniqueBloombergID>
    <ExtendedPrecisionPrice>99.0</ExtendedPrecisionPrice>
    <SecondaryTransactionTicketNumber>125385</SecondaryTransactionTicketNumber>
    <SystemDate>2017-08-10</SystemDate>
    <ImpactFlag>true</ImpactFlag>
    <SettlementLocationAbbreviation>DTC</SettlementLocationAbbreviation>
    <TraderAccountNumber>9</TraderAccountNumber>
    <TransactionType>OA  </TransactionType>
    <EnteredTicketUserId>********</EnteredTicketUserId>
    <AllocatedTicketUserId>********</AllocatedTicketUserId>
    <BloombergFunctions>
      <FunctionName>S   </FunctionName>
      <FunctionName />
      <FunctionName>OA  </FunctionName>
      <FunctionName />
    </BloombergFunctions>
    <OMSOrderNumber>125385</OMSOrderNumber>
    <MSRBReportable>true</MSRBReportable>
    <QuoteTypeIndicator>1</QuoteTypeIndicator>
    <IssueDate>2013-04-02</IssueDate>
    <SoftDollarFlag>N</SoftDollarFlag>
    <OMSOrderType>0</OMSOrderType>
    <OMSTimeInForce>0</OMSTimeInForce>
    <TIPFactorEstimated>A</TIPFactorEstimated>
    <TotalTradeAmount>1348759.32</TotalTradeAmount>
    <MasterAccount>DBAB</MasterAccount>
    <MasterAccountName>DBAB</MasterAccountName>
    <CTMMatchStatus>NR</CTMMatchStatus>
    <MatchDate>2017-08-10T19:06:00</MatchDate>
    <IsDirtyPrice>N</IsDirtyPrice>
    <TSAMIndicator>NR</TSAMIndicator>
    <TaxLotMethod>0</TaxLotMethod>
    <SecurityPrice>99.0</SecurityPrice>
    <SettlementAmount>1348759.32</SettlementAmount>
    <BloombergReferenceNumber>41693461140002829576</BloombergReferenceNumber>
    <FundCcyPrincipal>1348759.32</FundCcyPrincipal>
    <RedemptionCcyPrincipal>1348759.32</RedemptionCcyPrincipal>
    <SettlementCcyPrincipal>1348759.32</SettlementCcyPrincipal>
    <ExecutionPlatform>-1</ExecutionPlatform>
    <ClientAuth>N</ClientAuth>
    <BasketID>0</BasketID>
    <OMSTransactionID>1</OMSTransactionID>
    <LastLoginUUID>********</LastLoginUUID>
    <OriginalTktId>2829576</OriginalTktId>
    <SettlementCcyPrice>99.0</SettlementCcyPrice>
    <RTTMIndicator>NONE</RTTMIndicator>
    <RTTMReferenceID>***************</RTTMReferenceID>
    <CounterpartyEncodedLongName>RGV1dHNjaGUgQmFuayBBRyBOZXcgWW9yayBCcmFuY2g=</CounterpartyEncodedLongName>
    <AvgLife>2.615</AvgLife>
    <BloombergGlobalIdentifier>BBG004BN5MM0</BloombergGlobalIdentifier>
    <SecondHalfTdr2TdrTrade>N</SecondHalfTdr2TdrTrade>
    <CashReversalOffsetTicketIndicator>N</CashReversalOffsetTicketIndicator>
  </Common>
  <ExtendedFields>
    <Field calcrt="DS674" name="SECURITY_TYP2" />
    <Field calcrt="TX656" name="FEE_BASED_REPORTING" />
    <Field calcrt="TX657" name="ALTERNATIVE_TRADING_SYSTEM_MPID" />
    <Field calcrt="DX191" name="IS_PERPETUAL" />
    <Field calcrt="DS213" name="SECURITY_TYP">TERM</Field>
    <Field calcrt="TX658" name="TRANS_SETTLE_DATE_PLUS_1">********</Field>
    <Field calcrt="TX661" name="REPORTING_LKD_ID_COMPLEX_TRADE" />
    <Field calcrt="TX673" name="REPORTING_LINKAGE_TRADE_MARKER" />
    <Field calcrt="TX663" name="EXECUTION_VENUE_MIC_CODE" />
    <Field calcrt="TX664" name="REPORTING_LEI_CODE" />
    <Field calcrt="TX667" name="TRADE_UNIQUE_LIFECYCLE_ID" />
    <Field calcrt="TX669" name="MIFID_II_RPT_PST_TRADE_INDICATOR" />
    <Field calcrt="TX666" name="EXECUTION_VENUE_TRANSACTION_ID" />
    <Field calcrt="TX674" name="MIFID_II_TRADING_CAPACITY" />
    <Field calcrt="TX671" name="MIFID_II_DECISION_MAKER_WIN_FIRM" />
    <Field calcrt="TX672" name="MIFID_II_EXECUTION_WITHIN_FIRM" />
    <Field calcrt="TX668" name="MIFID_II_RPT_WAIVER_IND_TRA" />
    <Field calcrt="TX670" name="MIFID_II_RPT_SHORT_SELLING_IND" />
    <Field calcrt="TX665" name="TRADE_AS_OF_DATETIME_IN_UTC" />
  </ExtendedFields>
</TradeFeed>