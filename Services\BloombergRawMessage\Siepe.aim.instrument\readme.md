# Goal
Check if an instrument exists in the OMS. If it does not, query the AIM Security Master feed for that instrument and use the returned data to create the missing instrument in the OMS. The code to save new instruments in Reference and Core schemas (and map their IDs) already exists—no additional code is needed for that part.

---

## Project References
- Instrument.Entities  
- Siepe.Core.DataAccess  
- (plus any other existing projects or dependencies required by your solution)

---

## Overview of Existing Logic
The `Siepe.aim.instrument` library contains a class, `InstrumentManager`, which exposes a method:
```csharp
public void SaveInstrument(...)
SaveInstrument:

Creates a new instrument in the Reference schema.

Promotes that new instrument to the Core schema.

Maps the instrument IDs between Reference and Core.

Since this “save & map” logic is already implemented, you only need to invoke it once you have a fully populated IInstrument object.

Required Enhancements
Instrument-Existence Check

Add a new method (e.g. GetOrCreateInstrument) that accepts incoming Bloomberg AIM trade data (or its identifying fields) and determines if the corresponding instrument already exists in the OMS.

To check existence, call:


public IList<InstrumentInfo> SearchInstrument(string searchString, DateTime? tradeDate = null)
which lives in Siepe.Core.DataAccess\RefData\RefDataProvider.cs.

This method delegates to IRefDataRepository.SearchInstrument(...).

Behavior:

If SearchInstrument(...) returns a non-empty list, return the first matching InstrumentInfo (instrument already exists).

If no match is found, fetch the AIM feed and create a new instrument.

Fetch AIM Security Master Feed Message

Implement a helper method (e.g. FetchAIMSecurityMasterFeed) that executes the stored procedure:

pGetAIMSecurityMasterFeedMessage
Input parameters:

@tradeDate (DATETIME)

@identifierType (VARCHAR(20))

@identifierValue (VARCHAR(20))

Connection: Use the Core connection string via IDBAccess (or your existing data-access abstraction).

Output: A single XML string (for example):

<SMFMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xmlns:xsd="http://www.w3.org/2001/XMLSchema"
            pricingNumber="5436"
            date="2025-02-26">
  <Field calcrt="DS156" name="SECURITY_DES">GOOINS TL B 1L USD</Field>
  <Field calcrt="ID032" name="ID_CUSIP">38267BAE3</Field>
  <Field calcrt="ID005" name="ID_ISIN">US38267BAE39</Field>
  <Field calcrt="ID059" name="ID_BB_UNIQUE">COBL4842789</Field>
  <Field calcrt="DS031" name="ISSUE_DT">20250108</Field>
  <Field calcrt="DS035" name="MATURITY">20320108</Field>
  <Field calcrt="DS134" name="ISSUER">GOOSEHEAD INSURANCE HOLD</Field>
  <Field calcrt="DS086" name="CPN_TYP">FLOATING</Field>
  <Field calcrt="DS127" name="REFIX_FREQ">     12.00</Field>
  <Field calcrt="DS033" name="CPN">              7.81</Field>
  <Field calcrt="DS180" name="DAY_CNT_DES">ACT/360</Field>
  <Field calcrt="DS139" name="DAY_CNT">       2</Field>
  <Field calcrt="DS004" name="CRNCY">USD</Field>
  <Field calcrt="DY003" name="ID_BB_SEC_NUM_DES">GOOINS L 01/08/32 5</Field>
  <Field calcrt="DX650" name="CNTRY_OF_INCORPORATION">US</Field>
  <Field calcrt="DS002" name="NAME">GOOSEHEAD INSURANCE HOLD</Field>
  <Field calcrt="FL012" name="RESET_IDX">TSFR1M</Field>
  <Field calcrt="DS122" name="MARKET_SECTOR_DES">Corp</Field>
</SMFMessage>
Define a POCO for this XML (e.g., AIMSecurityMasterFeed.cs):



[XmlRoot("SMFMessage")]
public class AIMSecurityMasterFeed
{
    [XmlAttribute("pricingNumber")]
    public int PricingNumber { get; set; }

    [XmlAttribute("date")]
    public DateTime Date { get; set; }

    [XmlElement("Field")]
    public List<AIMField> Fields { get; set; }
}

public class AIMField
{
    [XmlAttribute("calcrt")]
    public string CalCrt { get; set; }

    [XmlAttribute("name")]
    public string Name { get; set; }

    [XmlText]
    public string Value { get; set; }
}
In FetchAIMSecurityMasterFeed, execute pGetAIMSecurityMasterFeedMessage, read the XML result into a string, and use XmlSerializer to deserialize into an AIMSecurityMasterFeed instance.

Transform AIMSecurityMasterFeed → Instrument.Entities.Instrument

After deserialization, map the fields from AIMSecurityMasterFeed.Fields into a new Instrument.Entities.Instrument (implements IInstrument):
Create a shell method for this, for now. We will implement this method later.
shell code to add-
var newInstrument = new Instrument.Entities.Instrument
{
    Cusip               = feed.Fields.First(f => f.Name == "ID_CUSIP").Value,
    Isin                = feed.Fields.First(f => f.Name == "ID_ISIN").Value,
    BloombergUnique     = feed.Fields.First(f => f.Name == "ID_BB_UNIQUE").Value,
    IssueDate           = DateTime.ParseExact(
                             feed.Fields.First(f => f.Name == "ISSUE_DT").Value,
                             "yyyyMMdd",
                             CultureInfo.InvariantCulture),
    Maturity            = DateTime.ParseExact(
                             feed.Fields.First(f => f.Name == "MATURITY").Value,
                             "yyyyMMdd",
                             CultureInfo.InvariantCulture),
    Issuer              = feed.Fields.First(f => f.Name == "ISSUER").Value,
    CouponType          = feed.Fields.First(f => f.Name == "CPN_TYP").Value,
    CouponRate          = Decimal.Parse(
                             feed.Fields.First(f => f.Name == "CPN").Value),
    DayCountConvention  = feed.Fields.First(f => f.Name == "DAY_CNT_DES").Value,
    Currency            = feed.Fields.First(f => f.Name == "CRNCY").Value,
    ResetIndex          = feed.Fields.First(f => f.Name == "RESET_IDX").Value,
    MarketSector        = feed.Fields.First(f => f.Name == "MARKET_SECTOR_DES").Value,
    // … map other required properties …
};
Once newInstrument is ready, invoke the existing save logic:


instrumentManager.SaveInstrument(newInstrument);
which will handle:

Inserting into the Reference schema.

Promoting to the Core schema.

Mapping Reference and Core IDs.

Implementation Steps (Condensed)
Add GetOrCreateInstrument(...) to InstrumentManager

Call SearchInstrument(searchString, tradeDate).

If found, return the existing InstrumentInfo.

If not found, call FetchAIMSecurityMasterFeed(tradeDate, identifierType, identifierValue).

Create FetchAIMSecurityMasterFeed(...)

Execute:

EXEC pGetAIMSecurityMasterFeedMessage @tradeDate, @identifierType, @identifierValue;
Read result into a string.

Deserialize into AIMSecurityMasterFeed using XmlSerializer.

Define POCO Classes

AIMSecurityMasterFeed.cs with AIMSecurityMasterFeed and AIMField.

Ensure correct [XmlRoot], [XmlAttribute], and [XmlElement] attributes match the SP’s XML.

Map Feed → Instrument.Entities.Instrument

Populate a new Instrument instance by extracting each <Field name="…">Value</Field>.

Call Existing Save Logic

Once the Instrument instance is ready, call:


instrumentManager.SaveInstrument(newInstrument);
(No extra code is needed for “save to Reference/Core” or “map IDs,” as it’s already implemented.)

Return the Result

GetOrCreateInstrument(...) should return either:

The existing InstrumentInfo if found.

Or the newly saved InstrumentInfo (after SaveInstrument(...) completes).

Example Workflow
Trade Message Arrives

Example: IdentifierType = "BB_UNIQUE", IdentifierValue = "COBL4842789", tradeDate = 2025-02-26.

Check OMS

SearchInstrument("COBL4842789", 2025-02-26) → empty result → proceed to create.

Fetch AIM Feed

FetchAIMSecurityMasterFeed(2025-02-26, "BB_UNIQUE", "COBL4842789") → returns <SMFMessage>…</SMFMessage> XML.

Deserialize

Populate AIMSecurityMasterFeed instance.

Build Instrument.Entities.Instrument

Map XML fields into a new Instrument object.

Save Instrument

Call instrumentManager.SaveInstrument(newInstrument) →

Inserts into Reference.

Promotes to Core.

Maps IDs.

Return Final Result

GetOrCreateInstrument(...) returns the saved InstrumentInfo (with Reference and Core IDs).

File/Folder Structure Suggestions
java
Copy
Edit
/Libraries
  /Siepe.aim.instrument
    InstrumentManager.cs            ← add GetOrCreateInstrument(...) and FetchAIMSecurityMasterFeed(...)
    AIMSecurityMasterFeed.cs        ← define POCO for feed XML
  /Siepe.Core.DataAccess
    /RefData
      RefDataProvider.cs            ← contains SearchInstrument(...)
      RefDataProviderSQL.cs         ← contains CreateInstrumentInRef(...) (already in use)
/Instrument.Entities
  Instrument.cs                     ← implements IInstrument, IComparisonSource
Additional Notes
Ensure proper error handling around the stored procedure call—if no XML or invalid XML is returned, log or throw a clear exception.

Use XmlSerializer with the correct settings so all <Field> elements deserialize correctly.

Confirm that date strings (YYYYMMDD) parse correctly into DateTime.

If multiple <Field> entries share the same name, decide whether to take the first match or handle duplicates explicitly.

Because the “save & map” code already exists inside SaveInstrument(...), there is no need to reimplement those steps—just invoke SaveInstrument(newInstrument).

If transactionality between Reference and Core is critical (e.g., roll back Reference if Core insert fails), verify that SaveInstrument(...) already handles that logic.

That completes the README content, reflecting that Reference/Core save logic already exists and guiding you to focus only on existence check, AIM‐feed retrieval, and POCO → IInstrument mapping.







