﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Siepe.GenevaUtility.Entities;
using Siepe.GenevaUtility;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class CreditDefaultSwapHandler : BaseInstrumentTypeHandler, IInstrumentTypeHandler
    {
        public override InvestmentInsertUpdateType GetInvestmentRecord(GenevaLoader loader)
        {
            return loader?.InvestmentRecords?.CreditDefaultSwap_InsertUpdate?.FirstOrDefault();
        }
    }
}
