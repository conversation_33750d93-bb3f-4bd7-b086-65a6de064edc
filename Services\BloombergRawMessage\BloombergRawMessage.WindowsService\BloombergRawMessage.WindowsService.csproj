<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\..\..\packages\NUnit.4.3.2\build\NUnit.props" Condition="Exists('..\..\..\packages\NUnit.4.3.2\build\NUnit.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>Siepe.Service.BloombergRawMessage.WindowsService</RootNamespace>
    <AssemblyName>Siepe.Service.BloombergRawMessage.WindowsService</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Practices.EnterpriseLibrary.Common, Version=6.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\EnterpriseLibrary.Common.6.0.1304.0\lib\NET45\Microsoft.Practices.EnterpriseLibrary.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.ServiceLocation, Version=1.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\CommonServiceLocator.1.3\lib\portable-net4+sl5+netcore45+wpa81+wp8\Microsoft.Practices.ServiceLocation.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Unity.3.5.1404.0\lib\net45\Microsoft.Practices.Unity.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.Configuration, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\Unity.3.5.1404.0\lib\net45\Microsoft.Practices.Unity.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Practices.Unity.RegistrationByConvention">
      <HintPath>..\..\..\packages\Unity.3.5.1404.0\lib\net45\Microsoft.Practices.Unity.RegistrationByConvention.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework, Version=4.3.2.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\NUnit.4.3.2\lib\net462\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework.legacy, Version=4.3.2.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\NUnit.4.3.2\lib\net462\nunit.framework.legacy.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SqlClient, Version=4.6.1.6, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
      <HintPath>..\..\..\packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Siepe.Shared.Service.PublishSubscribe.Contract">
      <HintPath>..\..\..\packages\Siepe.Services.PublishSubscribe.Assemblies.4.5.20160825.1\lib\Siepe.Shared.Service.PublishSubscribe.Contract.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BloombergRawMessageService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="BloombergRawMessageService.Designer.cs">
      <DependentUpon>BloombergRawMessageService.cs</DependentUpon>
    </Compile>
    <Compile Include="MessageProcessor.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ServiceBootstrapper.cs" />
    <Compile Include="ProjectInstaller.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ProjectInstaller.Designer.cs">
      <DependentUpon>ProjectInstaller.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility.v1\DBUtility.v1.csproj">
      <Project>{F933A665-6087-4FBE-B724-2F93E75943C1}</Project>
      <Name>DBUtility.v1</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\DBUtility\DBUtility\DBUtility.csproj">
      <Project>{9354c545-4a3a-48c1-91c5-387a1d10b6dc}</Project>
      <Name>DBUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Biz\Expenses.Biz.csproj">
      <Project>{b36a1f71-063d-4fe1-b2ce-f4472d872eba}</Project>
      <Name>Expenses.Biz</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Engine\Expenses.Engine.csproj">
      <Project>{c739b218-01f5-494a-883a-a8234134e017}</Project>
      <Name>Expenses.Engine</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Expenses\Expenses.Entities\Expenses.Entities.csproj">
      <Project>{3668404e-399d-4203-b8d4-eab9f0cb3e60}</Project>
      <Name>Expenses.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility.Entities\GenevaUtility.Entities.csproj">
      <Project>{2d7e7414-899b-4448-9901-ad9635399280}</Project>
      <Name>GenevaUtility.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\GenevaUtility\GenevaUtility\GenevaUtility.csproj">
      <Project>{825c9037-535e-4daf-9c0c-3422a43d973c}</Project>
      <Name>GenevaUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Data\Siepe.Instruments.Data.csproj">
      <Project>{0bb29d1e-443b-4b4f-9fdd-f44e08ae937a}</Project>
      <Name>Siepe.Instruments.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Instruments\Siepe.Instruments.Entities\Siepe.Instruments.Entities.csproj">
      <Project>{5027171f-0eb6-4204-8d2d-eb2db6c8832d}</Project>
      <Name>Siepe.Instruments.Entities</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\IssuerCreation\IssuerCreation\IssuerCreation.csproj">
      <Project>{ac088000-023b-4809-b3fb-d2893ff81b99}</Project>
      <Name>IssuerCreation</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\PubSubUtility\PubSubUtility\PubSubUtility.csproj">
      <Project>{F139C84B-FBA0-4BE7-BF4E-21B24757FD87}</Project>
      <Name>PubSubUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.DataLayer\Siepe.RulesEngine.DataLayer.csproj">
      <Project>{26b98504-bf45-45f7-a92e-b202d42c7c29}</Project>
      <Name>Siepe.RulesEngine.DataLayer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.IronPython\Siepe.RulesEngine.IronPython.csproj">
      <Project>{eddce863-48f1-4248-88ba-7ef47d2f23d3}</Project>
      <Name>Siepe.RulesEngine.IronPython</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\RulesEngine\Siepe.RulesEngine.Services\Siepe.RulesEngine.Services.csproj">
      <Project>{86db5604-0697-4eb7-aadc-6e070237be57}</Project>
      <Name>Siepe.RulesEngine.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Serialization\JsonSerializer\JsonSerializer.csproj">
      <Project>{8c7b95f4-9fee-4ee1-976e-ed1eacc59a3f}</Project>
      <Name>JsonSerializer</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\Serialization\Serialization\Serialization.csproj">
      <Project>{601390c0-ed1a-4d53-a2fe-1497ecaa8fdb}</Project>
      <Name>Serialization</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Libraries\UnityUtility\UnityUtility\UnityUtility.csproj">
      <Project>{1a6f5cc7-abf6-4a07-9c44-8aa701cc081d}</Project>
      <Name>UnityUtility</Name>
    </ProjectReference>
    <ProjectReference Include="..\Contract\Contract.csproj">
      <Project>{AE7F4C21-DA16-4598-9AAF-55BBF4F899FF}</Project>
      <Name>Contract</Name>
    </ProjectReference>
    <ProjectReference Include="..\Impl\Impl.csproj">
      <Project>{5B33058A-3455-4CFB-B72C-0BEA1B93CF5E}</Project>
      <Name>Impl</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\..\packages\NUnit.4.3.2\build\NUnit.props')" Text="$([System.String]::Format('$(ErrorText)', '..\..\..\packages\NUnit.4.3.2\build\NUnit.props'))" />
  </Target>
</Project>