using Microsoft.Practices.Unity;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Service.BloombergRawMessage.Web;
using Siepe.Shared.Service.PublishSubscribe.Contract.Dto;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BloombergRawData.Console.Local
{
    class Program
    {
        static void Main(string[] args)
        {
            var bootstrapper = new ConsoleBootstrapper();
            var container = new UnityContainer();
            bootstrapper.Bootstrap(container);


            var service = container.Resolve<ServiceRawMessage>();

            service.Publish(new Message
            {
                BaseSubject = "TradeFee.RawMessage",
                Payload = Encoding.UTF8.GetBytes(GetTestXml())
            });

        }

        private static string GetTestXml()
        {
            return File.ReadAllText("TestInput.xml");
        }
    }
}

