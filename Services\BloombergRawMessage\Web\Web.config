﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="CoreConnectionString" connectionString="Data Source=005sql04.highland.aws,52155;Initial Catalog=HCM;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />
    <add name="FeedsConnectionString" connectionString="Data Source=005sql04.highland.aws,52155;Initial Catalog=DataFeeds;Trusted_Connection=true;Connection Timeout=0;" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <appSettings>
    <add key="Domain" value="Development" />
    <add key="Version" value="1.0" />
    <add key="TransactionConfig" value="TradeFeedConfiguration" />
    <add key="DefaultIssuer" value="Unknown Issuer" />
    <add key="Domain" value="DEV" />
    <add key="Location" value="Dallas" />
    <add key="Company" value="HoldCo" />
    <add key="ProcessSecMaster" value="true" />
  </appSettings>
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <remove name="Default" />
        <add name="ServiceTraceListener" type="Siepe.Shared.Service.Common.ServiceModelEx.Tracers.MSMQLoggingTraceListener, Siepe.Shared.Service.Common.ServiceModelEx" />
      </listeners>
    </trace>
  </system.diagnostics>
  <system.serviceModel>
    <serviceHostingEnvironment minFreeMemoryPercentageToActivateService="0" multipleSiteBindingsEnabled="true" />
    <client>
      <endpoint address="net.msmq://betaservices.highland.aws/private/Logging/SvcLogging.svc" binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" behaviorConfiguration="ImpersonationBehavior" contract="IServiceLoggingEntryOnly" name="Logging_netMsmqBinding" />
      <endpoint address="net.msmq://betaservices.highland.aws/private/PublishSubscribeSiepe/ServicePublication.svc" binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServicePublication.IServicePublication" name="Publication" />
      <endpoint address="http://localhost/Configuration/ServiceConfiguration.svc/basic" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IServiceConfiguration" contract="Siepe.Shared.Service.Configuration.Adapter.IServiceConfiguration" name="Configuration" />
      <endpoint address="net.tcp://betaservices.highland.aws/InstrumentEditorSiepe/ServiceInstrumentEditor.svc" binding="netTcpBinding" bindingConfiguration="netTcpBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServiceInstrumentEditor.IServiceInstrumentEditor" name="InstrumentEditor">
        <identity>
          <userPrincipalName value="<EMAIL>" />
        </identity>
      </endpoint>
      <endpoint address="net.tcp://betaservices.highland.aws/LegalEntitySiepe/ServiceLegalEntity.svc/LegalEntity" binding="netTcpBinding" bindingConfiguration="netTcpBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServiceLegalEntity.IServiceLegalEntity" name="LegalEntity">
        <identity>
          <userPrincipalName value="<EMAIL>" />
        </identity>
      </endpoint>
    </client>
    <services>
      <service name="Siepe.Service.BloombergRawMessage.Web.ServiceRawTrade" behaviorConfiguration="MEX Enabled">
        <endpoint binding="wsHttpBinding" bindingConfiguration="ReliableTransactionalHTTP" contract="Siepe.Service.BloombergRawMessage.Contract.IServiceRawTrade" />
      </service>
      <service name="Siepe.Service.BloombergRawMessage.Web.ServiceRawMessage" behaviorConfiguration="MEX Enabled">
        <endpoint binding="wsHttpBinding" bindingConfiguration="ReliableTransactionalHTTP" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
        <endpoint binding="netTcpBinding" bindingConfiguration="netTcpBinding" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
        <!--<endpoint binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />-->
        <endpoint binding="mexHttpBinding" contract="IMetadataExchange" address="mex" />
      </service>
    </services>
    <behaviors>
      <serviceBehaviors>
        <behavior name="MEX Enabled">
          <serviceMetadata httpGetEnabled="true" />
          <serviceCredentials>
            <userNameAuthentication userNamePasswordValidationMode="Windows" />
          </serviceCredentials>
          <dataContractSerializer maxItemsInObjectGraph="65536000" />
        </behavior>
      </serviceBehaviors>
      <endpointBehaviors>
        <behavior name="ImpersonationBehavior">
          <clientCredentials>
            <windows allowedImpersonationLevel="Identification" />
          </clientCredentials>
        </behavior>
      </endpointBehaviors>
    </behaviors>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_IServiceConfiguration" />
      </basicHttpBinding>
      <wsHttpBinding>
        <binding name="ReliableTransactionalHTTP" transactionFlow="true" maxBufferPoolSize="2147483647" maxReceivedMessageSize="2147483647">
          <readerQuotas maxStringContentLength="2147483647" />
          <reliableSession enabled="true" />
        </binding>
      </wsHttpBinding>
      <netTcpBinding>
        <binding name="netTcpBinding" portSharingEnabled="true" maxReceivedMessageSize="6553600">
          <readerQuotas maxArrayLength="163840" />
          <security mode="Transport">
            <transport clientCredentialType="Windows" />
          </security>
        </binding>
      </netTcpBinding>
      <netMsmqBinding>
        <binding name="netMsmqBinding" exactlyOnce="false" maxReceivedMessageSize="2147483647">
          <readerQuotas maxArrayLength="163840" />
          <security mode="None" />
        </binding>
      </netMsmqBinding>
    </bindings>
  </system.serviceModel>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
  <system.web>
    <identity impersonate="false" />
    <customErrors mode="Off" />
    <pages buffer="true" enableSessionState="false" enableViewState="false" enableViewStateMac="false" smartNavigation="false" controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID" />
    <compilation defaultLanguage="c#" debug="true" targetFramework="4.7.2">
      <assemblies>
        <add assembly="Microsoft.Transactions.Bridge, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="SMDiagnostics, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Security, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.IdentityModel.Selectors, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.DirectoryServices, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Web.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Transactions, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
        <add assembly="System.Messaging, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.ServiceProcess, Version=*******, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
        <add assembly="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=B77A5C561934E089" />
      </assemblies>
    </compilation>
  </system.web>
  <system.webServer>
    <defaultDocument>
      <files>
        <add value="ServiceRawMessage.svc" />
      </files>
    </defaultDocument>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="nunit.framework" publicKeyToken="96d09a1eb7f44a77" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.6.4.14350" newVersion="2.6.4.14350" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Caching" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-5.0.505.0" newVersion="5.0.505.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.Unity" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.0" newVersion="3.5.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.ServiceLocation" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.3.0.0" newVersion="1.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.Unity.Interception" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.0" newVersion="3.5.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.Unity.Configuration" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.0" newVersion="3.5.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>