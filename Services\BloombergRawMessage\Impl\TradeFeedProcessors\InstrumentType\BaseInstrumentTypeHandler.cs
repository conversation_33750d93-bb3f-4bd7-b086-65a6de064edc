﻿using Microsoft.Practices.Unity;
using Siepe.GenevaUtility;
using Siepe.GenevaUtility.Entities;
using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Shared.DBUtility;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public abstract class BaseInstrumentTypeHandler
    {
        [Dependency]
        public IGenevaDataProvider _dataProvider { get; set; }

        public virtual InvestmentInsertUpdateType GetInvestmentRecord(GenevaLoader loader)
        {
            return loader?.InvestmentRecords?.Equity_InsertUpdate?.FirstOrDefault();
        }

        public virtual string GetInvestment(string bloombergId)
        {
            string investmentVal = "";
            GenevaLoader loader = _dataProvider.GetInvestmentXmlByBloombergID(bloombergId);
            InvestmentInsertUpdateType investment = GetInvestmentRecord(loader);
            if (investment != null)
            {
                if (!string.IsNullOrWhiteSpace(investment.AltKey1))
                {
                    investmentVal = investment.AltKey1;
                }
                else if (!string.IsNullOrWhiteSpace(investment.KeyValue.Value))
                {
                    investmentVal = investment.KeyValue.Value;
                }
            }
            return investmentVal;
        }

        public virtual string GetQuantity(Trade trade)
        {
            return trade.TradeDetails.TradeAmount;
        }
    }
}
