<?xml version="1.0"?>
<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
	<system.web>
	</system.web>
	<system.serviceModel>
		<client xdt:Transform="Replace">
			<endpoint address= "net.msmq://services.hcmlp.com/private/Logging/SvcLogging.svc" binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" behaviorConfiguration="ImpersonationBehavior" contract="IServiceLoggingEntryOnly" name="Logging_netMsmqBinding" />
			<endpoint address= "net.msmq://services.hcmlp.com/private/PublishSubscribeSiepe/ServicePublication.svc" binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServicePublication.IServicePublication" name="Publication" />
			<endpoint address="net.tcp://services.hcmlp.com/ConfigurationSiepe/ServiceConfiguration.svc" binding="netTcpBinding" bindingConfiguration="netTcpBinding" behaviorConfiguration="ImpersonationBehavior" contract="Siepe.Shared.Service.Configuration.Adapter.IServiceConfiguration" name="Configuration">
				<identity>
					<userPrincipalName value="<EMAIL>" />
				</identity>
			</endpoint>
			<endpoint address="net.tcp://services.hcmlp.com/InstrumentEditorSiepe/ServiceInstrumentEditor.svc" binding="netTcpBinding" bindingConfiguration="netTcpBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServiceInstrumentEditor.IServiceInstrumentEditor" name="InstrumentEditor">
				<identity>
					<userPrincipalName value="<EMAIL>"/>
				</identity>
			</endpoint>
			<endpoint address="net.tcp://services.hcmlp.com/LegalEntitySiepe/ServiceLegalEntity.svc/LegalEntity" binding="netTcpBinding" bindingConfiguration="netTcpBinding" behaviorConfiguration="ImpersonationBehavior" contract="ServiceLegalEntity.IServiceLegalEntity" name="LegalEntity">
				<identity>
					<userPrincipalName value="<EMAIL>"/>
				</identity>
			</endpoint>
		</client>
		<services xdt:Transform="Replace">
			<service name="Siepe.Service.BloombergRawMessage.Web.ServiceRawMessage" behaviorConfiguration="MEX Enabled">
				<endpoint binding="wsHttpBinding" bindingConfiguration="ReliableTransactionalHTTP" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
				<endpoint binding="netTcpBinding" bindingConfiguration="netTcpBinding" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
				<endpoint binding="netMsmqBinding" bindingConfiguration="netMsmqBinding" contract="Siepe.Shared.Service.PublishSubscribe.Contract.IServicePublication" />
				<endpoint binding="mexHttpBinding" contract="IMetadataExchange" address="mex" />
			</service>
			<service name="Siepe.Service.BloombergRawMessage.Web.ServiceRawTrade" behaviorConfiguration="MEX Enabled">
				<endpoint binding="wsHttpBinding" bindingConfiguration="ReliableTransactionalHTTP" contract="Siepe.Service.BloombergRawMessage.Contract.IServiceRawTrade" />
			</service>
		</services>
	</system.serviceModel>
</configuration>
