﻿using Siepe.Service.BloombergRawMessage.Contract;
using Siepe.Shared.DBUtility;
using Siepe.Shared.Utils.Configuration;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public interface ITradeFeedConfigurationProvider
    {
        TradeFeedConfiguration GetConfiguration();
    }

    public class TradeFeedConfigurationProvider : ITradeFeedConfigurationProvider
    {

        private readonly ISqlDbAccess sqlDbAccess;

        public TradeFeedConfigurationProvider(ISqlDbAccess sqlDbAccess)
        {
            this.sqlDbAccess = sqlDbAccess;
        }

        public TradeFeedConfiguration GetConfiguration()
        {
            string config = ConfigurationManager.AppSettings["TransactionConfig"];
            return sqlDbAccess.GetFromXml<TradeFeedConfiguration>("Enterprise.dbo.pConfigurationSectionRawXml",
                new[] { new SqlParameter("@configurationName", config) });
        }
    }
}
