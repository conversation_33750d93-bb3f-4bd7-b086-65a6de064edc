using System;
using System.ServiceProcess;
using System.Diagnostics;

namespace Siepe.Service.BloombergRawMessage.WindowsService
{
    static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        static void Main(string[] args)
        {
            try
            {
                if (Environment.UserInteractive)
                {
                    // Running as console application for debugging
                    Console.WriteLine("Bloomberg Raw Message Service - Console Mode");

                    var service = new BloombergRawMessageService();
                    service.StartConsole();

                    Console.WriteLine("Service started. Press any key to stop...");
                    Console.ReadKey();

                    service.StopConsole();
                    Console.WriteLine("Service stopped.");
                }
                else
                {
                    // Running as Windows Service
                    ServiceBase[] ServicesToRun;
                    ServicesToRun = new ServiceBase[]
                    {
                        new BloombergRawMessageService()
                    };
                    ServiceBase.Run(ServicesToRun);
                }
            }
            catch (Exception ex)
            {
                Trace.TraceError($"Bloomberg Raw Message Service startup error: {ex.Message}");
                Trace.TraceError($"Stack trace: {ex.StackTrace}");
                
                if (Environment.UserInteractive)
                {
                    Console.WriteLine($"Error: {ex.Message}");
                    Console.WriteLine("Press any key to exit...");
                    Console.ReadKey();
                }
            }
        }
    }
}
