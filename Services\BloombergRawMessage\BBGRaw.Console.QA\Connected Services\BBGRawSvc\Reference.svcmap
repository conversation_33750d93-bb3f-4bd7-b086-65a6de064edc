<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="abb570b9-f8cf-49a5-8c44-00fc93685f2c" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://qaservices.highland.aws/BloombergRawData/" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="ServiceRawMessage.wsdl" MetadataType="Wsdl" ID="918f8558-7851-450f-8079-a97b577c860a" SourceId="1" SourceUrl="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?wsdl=wsdl0" />
    <MetadataFile FileName="ServiceRawMessage.xsd" MetadataType="Schema" ID="983f8d5f-794b-4c12-8575-1edd811ac1b3" SourceId="1" SourceUrl="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?xsd=xsd0" />
    <MetadataFile FileName="ServiceRawMessage1.xsd" MetadataType="Schema" ID="7598b7b7-29ae-46dd-8066-e796a1b1f073" SourceId="1" SourceUrl="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?xsd=xsd1" />
    <MetadataFile FileName="ServiceRawMessage2.xsd" MetadataType="Schema" ID="ca31daab-139a-4642-95e0-f26670a098a0" SourceId="1" SourceUrl="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?xsd=xsd2" />
    <MetadataFile FileName="ServiceRawMessage1.wsdl" MetadataType="Wsdl" ID="845c7c3b-4778-4045-a0e4-7b19d48d1d9e" SourceId="1" SourceUrl="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?wsdl" />
    <MetadataFile FileName="ServiceRawMessage.disco" MetadataType="Disco" ID="7e704a9c-46f6-4437-9298-1b9849710038" SourceId="1" SourceUrl="http://qaservices.highland.aws/BloombergRawData/ServiceRawMessage.svc?disco" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>