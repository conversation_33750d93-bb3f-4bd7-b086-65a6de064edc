﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Siepe.GenevaUtility.Entities;
using Siepe.Shared.DBUtility;
using Rhino.Mocks;
using Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor;
using System.Data.SqlClient;
using System.Data;

namespace Test
{
    [TestClass]
    public class CfdSwapTest
    {
        private ISqlDbAccess mockSqlDbAccess;

        [TestInitialize]
        public void Initialize()
        {
            mockSqlDbAccess = MockRepository.GenerateMock<ISqlDbAccess>();
        }

        [TestMethod]
        public void DoesNothingOnNonCfd()
        {
            var rawTrade = new RawTrade
            {
                CfdSwapFlag = false
            };

            var cfdSwapProcessor = new CfdSwapProcessor(mockSqlDbAccess);

            cfdSwapProcessor.Process(rawTrade);
            mockSqlDbAccess.AssertWasNotCalled(x => x.ExecuteScalar(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything));
        }

        [TestMethod]
        public void DoesNotSaveIfSwapExists()
        {
            var rawTrade = new RawTrade
            {
                CfdSwapFlag = true,
                BloombergID = "id",
                Portfolio = "portfolio",
                LocationAccount = "locationAmount"
            };

            mockSqlDbAccess.Stub(x => x.ExecuteScalar(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything)).Return(true);

            var cfdSwapProcessor = new CfdSwapProcessor(mockSqlDbAccess);

            cfdSwapProcessor.Process(rawTrade);
            mockSqlDbAccess.AssertWasCalled(x => x.ExecuteScalar(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything));

            mockSqlDbAccess.AssertWasNotCalled(x => x.ExecuteNonQuery(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything));
        }

        [TestMethod]
        public void DoesSavesIfSwapDoesNotExists()
        {
            var rawTrade = new RawTrade
            {
                CfdSwapFlag = true,
                BloombergID = "id",
                Portfolio = "portfolio",
                LocationAccount = "locationAmount"
            };

            mockSqlDbAccess.Stub(x => x.ExecuteScalar(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything)).Return(false);

            var cfdSwapProcessor = new CfdSwapProcessor(mockSqlDbAccess);

            cfdSwapProcessor.Process(rawTrade);
            mockSqlDbAccess.AssertWasCalled(x => x.ExecuteScalar(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything));

            mockSqlDbAccess.AssertWasCalled(x => x.ExecuteNonQuery(Arg<string>.Is.Anything, Arg<SqlParameter[]>.Is.Anything, Arg<CommandType>.Is.Anything, Arg<SqlTransaction>.Is.Anything));
        }
    }
}
