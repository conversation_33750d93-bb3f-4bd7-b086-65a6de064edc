<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="Siepe.Shared.Service.PublishSubscribe.Contract" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="Siepe.Shared.Service.PublishSubscribe.Contract" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="Siepe.Shared.Service.PublishSubscribe.Contract/Imports">
      <xsd:import schemaLocation="http://localhost/BloombergRawData/ServiceRawMessage.svc?xsd=xsd0" namespace="Siepe.Shared.Service.PublishSubscribe.Contract" />
      <xsd:import schemaLocation="http://localhost/BloombergRawData/ServiceRawMessage.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost/BloombergRawData/ServiceRawMessage.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/Siepe.Shared.Service.PublishSubscribe.Contract.Dto" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IServicePublication_Publish_InputMessage">
    <wsdl:part name="parameters" element="tns:Publish" />
  </wsdl:message>
  <wsdl:portType name="IServicePublication">
    <wsdl:operation name="Publish">
      <wsdl:input wsaw:Action="Siepe.Shared.Service.PublishSubscribe.Contract/IServicePublication/Publish" message="tns:IServicePublication_Publish_InputMessage" />
    </wsdl:operation>
  </wsdl:portType>
</wsdl:definitions>