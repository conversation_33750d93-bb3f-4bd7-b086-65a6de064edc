﻿using Siepe.GenevaUtility;
using Siepe.GenevaUtility.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl
{
    public class EquityHandler : BaseInstrumentTypeHandler, IInstrumentTypeHandler
    {
        public override InvestmentInsertUpdateType GetInvestmentRecord(GenevaLoader loader)
        {
            return loader?.InvestmentRecords?.Equity_InsertUpdate?.FirstOrDefault();
        }
    }
}
