﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Impl.TradeFeedProcessors.RawTradeProcessor
{
    public class RawTradeProcessorProvider : IRawTradeProcessorProvider
    {
        private List<IRawTradeProcessor> rawTradeProcessors;

        public RawTradeProcessorProvider(IRawTradeProcessor[] rawTradeProcessor)
        {
            this.rawTradeProcessors = rawTradeProcessor.ToList();
        }

        public IEnumerable<IRawTradeProcessor> GetRawTradeProcessors()
        {
            return rawTradeProcessors;
        }
    }
}
