﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.Text;
using System.Threading.Tasks;

namespace Siepe.Service.BloombergRawMessage.Contract
{
    [DataContract]
    public class TransactionTypeConfiguration
    {
        [DataMember]
        public string Raw { get; set; }

        [DataMember]
        public string Flag { get; set; }

        [DataMember]
        public string TransactionType { get; set; }

        [DataMember]
        public string CreateMessageType { get; set; }

        [DataMember]
        public string CancelMessageType { get; set; }
    }
}
