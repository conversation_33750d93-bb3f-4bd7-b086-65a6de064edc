# Configuration Migration Guide

## Overview
This document outlines the comprehensive changes made to fix the dependency on `ConfigurationManager.AppSettings` and migrate the Bloomberg Raw Message background service to use .NET Core configuration patterns.

## Problems Addressed

### 1. **Critical Configuration Issue**
- `TradeFeedConfigurationProvider.cs` was using `ConfigurationManager.AppSettings["TransactionConfig"]`
- This would fail in the background service environment as it doesn't have app.config
- The "TransactionConfig" value is essential for loading trade feed configuration from the database

### 2. **Missing Configuration Values**
- Background service was missing several configuration values that were present in app.config files
- Values like Domain, Version, DefaultIssuer, Location were not available

## Solution Implementation

### 1. **Enhanced Configuration Model**
Created `BloombergRawMessageSettings.cs` with all required configuration properties:
- `TransactionConfig`: Database configuration name (critical for trade processing)
- `Domain`: Environment/domain identifier
- `Version`: Service version
- `DefaultIssuer`: Default issuer for instruments
- `Location`: Office/location identifier
- `Company`: Company name
- `ProcessSecMaster`: Security master processing flag
- `EnableFeeProcessing`: Fee processing control flag

### 2. **Updated appsettings.json**
Added missing configuration values to `BloombergRawMessageSettings` section:
```json
"BloombergRawMessageSettings": {
  "Company": "HoldCo",
  "ProcessSecMaster": true,
  "EnableFeeProcessing": false,
  "TransactionConfig": "TradeFeedConfiguration",
  "Domain": "Development",
  "Version": "1.0",
  "DefaultIssuer": "Unknown Issuer",
  "Location": "Dallas"
}
```

### 3. **New Configuration-Aware Provider**
Created `ConfigurableTradeFeedConfigurationProvider.cs`:
- Implements `ITradeFeedConfigurationProvider` interface
- Uses `IOptions<BloombergRawMessageSettings>` instead of `ConfigurationManager`
- Maintains same functionality as original but with .NET Core configuration
- Includes proper error handling for missing configuration

### 4. **Service Information Provider**
Created `IServiceInfoProvider` and `ServiceInfoProvider`:
- Replaces Web service's `GetDomainName()` and `GetVersion()` functionality
- Provides centralized access to all configuration values
- Can be injected into any service that needs configuration information

### 5. **Updated Service Registration**
Modified `ServiceBootstrapper.cs`:
- Added configuration binding: `services.Configure<BloombergRawMessageSettings>`
- Registered new configuration-aware services
- Replaced `TradeFeedConfigurationProvider` with `ConfigurableTradeFeedConfigurationProvider`

### 6. **Cleanup**
- Removed unused `System.Configuration` import from `EquityHandler.cs`

## Migration Benefits

1. **Compatibility**: Background service now works without app.config dependency
2. **Maintainability**: Strongly-typed configuration with IntelliSense support
3. **Flexibility**: Easy to add new configuration values or change existing ones
4. **Testability**: Configuration can be easily mocked for unit tests
5. **Consistency**: Follows .NET Core configuration patterns

## Usage Examples

### Accessing Configuration in Services
```csharp
public class MyService
{
    private readonly BloombergRawMessageSettings _settings;
    
    public MyService(IOptions<BloombergRawMessageSettings> settings)
    {
        _settings = settings.Value;
    }
    
    public void DoSomething()
    {
        var transactionConfig = _settings.TransactionConfig;
        var domain = _settings.Domain;
        // Use configuration values...
    }
}
```

### Using Service Info Provider
```csharp
public class MyService
{
    private readonly IServiceInfoProvider _serviceInfo;
    
    public MyService(IServiceInfoProvider serviceInfo)
    {
        _serviceInfo = serviceInfo;
    }
    
    public void LogServiceInfo()
    {
        var serviceName = _serviceInfo.GetServiceName();
        var version = _serviceInfo.GetVersion();
        var domain = _serviceInfo.GetDomainName();
        // Log or use service information...
    }
}
```

## Testing the Changes

1. **Verify Configuration Loading**: Ensure all configuration values are properly loaded from appsettings.json
2. **Test Trade Processing**: Verify that `TradeFeedConfigurationProvider.GetConfiguration()` works correctly
3. **Check Service Info**: Confirm that service information methods return expected values
4. **Integration Testing**: Test the complete message processing pipeline

## Future Considerations

1. **Environment-Specific Settings**: Consider using `appsettings.{Environment}.json` for environment-specific overrides
2. **Configuration Validation**: Add data annotations to `BloombergRawMessageSettings` for validation
3. **Hot Reload**: Consider implementing `IOptionsMonitor` for configuration changes without restart
4. **Secrets Management**: Move sensitive configuration to secure storage (Azure Key Vault, etc.)
